package com.huobi.view

import android.content.Context
import android.util.AttributeSet
import androidx.core.widget.NestedScrollView

/**
 * 自定义NestedScrollView
 */
class MyNestedScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : NestedScrollView(context, attrs, defStyleAttr) {

    // 可以在这里添加自定义的滚动逻辑
    // 目前保持与标准NestedScrollView相同的行为
    
}
