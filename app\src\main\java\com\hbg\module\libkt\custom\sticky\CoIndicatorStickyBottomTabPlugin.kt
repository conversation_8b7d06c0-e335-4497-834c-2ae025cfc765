package com.hbg.module.libkt.custom.sticky

import android.content.Context
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.viewpager2.widget.ViewPager2
import com.hbg.module.libkt.custom.indicator.CoIndicator

/**
 * 专门支持CoIndicator的吸底Tab插件扩展
 * 
 * 这个类扩展了基础的StickyBottomTabPlugin，专门处理CoIndicator组件的特殊需求
 */
class CoIndicatorStickyBottomTabPlugin private constructor(
    context: Context,
    config: Config
) : StickyBottomTabPlugin(context, config) {
    
    companion object {
        /**
         * 为CoIndicator创建插件的便捷方法
         */
        fun create(
            context: Context,
            coordinatorLayout: androidx.coordinatorlayout.widget.CoordinatorLayout,
            appBarLayout: com.google.android.material.appbar.AppBarLayout,
            coIndicator: CoIndicator,
            viewPager: ViewPager2,
            tabTitles: List<String>,
            animationDuration: Long = 300L,
            scrollToPosition: Float = 0.33f
        ): CoIndicatorStickyBottomTabPlugin {
            
            val config = Config(
                coordinatorLayout = coordinatorLayout,
                appBarLayout = appBarLayout,
                originalTabLayout = coIndicator,
                viewPager = viewPager,
                animationDuration = animationDuration,
                scrollToPosition = scrollToPosition,
                onTabClickListener = null
            )
            
            return CoIndicatorStickyBottomTabPlugin(context, config).apply {
                // 设置CoIndicator特有的配置
                setTabTitles(tabTitles)
            }
        }
    }
    
    private var tabTitles: List<String> = emptyList()
    
    /**
     * 设置Tab标题列表
     */
    fun setTabTitles(titles: List<String>) {
        this.tabTitles = titles
    }
    
    /**
     * 创建CoIndicator的副本
     */
    fun createCoIndicatorCopy(originalCoIndicator: CoIndicator): CoIndicator {
        val stickyIndicator = CoIndicator(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                0,
                context.resources.getDimensionPixelSize(android.R.dimen.app_icon_size),
                1f
            )
            setPadding(
                context.resources.getDimensionPixelSize(android.R.dimen.notification_large_icon_width) / 10,
                0, 0, 0
            )
        }

        // 复制Tab内容
        tabTitles.forEach { title ->
            stickyIndicator.addTab(title)
        }

        // 同步当前选中的Tab
        val currentItem = config.viewPager?.currentItem ?: 0
        if (currentItem < stickyIndicator.tabCount) {
            stickyIndicator.getTabAt(currentItem)?.select()
        }

        // 设置点击监听
        stickyIndicator.setOnTabSelectedListener { position ->
            handleCoIndicatorTabClick(position)
        }

        return stickyIndicator
    }
    
    /**
     * 处理CoIndicator的Tab点击
     */
    private fun handleCoIndicatorTabClick(position: Int) {
        android.util.Log.d("CoIndicatorStickyPlugin", "CoIndicator Tab点击: position=$position")

        // 切换ViewPager页面
        config.viewPager?.setCurrentItem(position, true)

        // 点击吸底Tab时需要滚动到原始Tab位置
        performScrollToPosition()

        android.util.Log.d("CoIndicatorStickyPlugin", "CoIndicator Tab切换完成，开始滚动到原始Tab位置")
    }

    /**
     * 设置CoIndicator的状态同步
     */
    fun setupCoIndicatorSync(stickyIndicator: CoIndicator) {
        config.viewPager?.let { viewPager ->
            val pageChangeCallback = object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    // 同步吸底CoIndicator的选中状态
                    if (isBottomTabVisible() && position < stickyIndicator.tabCount) {
                        stickyIndicator.getTabAt(position)?.select()
                    }
                }
            }
            viewPager.registerOnPageChangeCallback(pageChangeCallback)
        }
    }

    override fun performScrollToPosition() {
        // 调用父类的滚动方法
        super.performScrollToPosition()
    }
}
