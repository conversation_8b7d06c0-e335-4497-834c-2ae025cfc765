# StickyBottomTabPlugin - 吸底Tab插件

一个可复用的Android吸底Tab插件，当原始Tab滚出屏幕底部时，在屏幕底部显示粘性Tab，保持导航的可用性。

## 🎯 核心特性

- **即插即用**：通过几行代码就能集成吸底Tab效果
- **高度可配置**：支持自定义动画时长、滚动位置、样式等
- **完整的生命周期管理**：自动处理资源创建和销毁
- **流畅的动画效果**：300ms的滑入/滑出动画
- **智能的交互逻辑**：点击吸底Tab后自动滚动到合适位置
- **多Tab组件支持**：支持TabLayout、CoIndicator等不同Tab组件
- **状态同步**：吸底Tab与ViewPager2保持完全同步

## 🚀 快速开始

### 基础用法

```kotlin
// 1. 创建插件
val plugin = StickyBottomTabPlugin.Builder(context)
    .setCoordinatorLayout(coordinatorLayout)
    .setAppBarLayout(appBarLayout)
    .setOriginalTabLayout(tabLayout)
    .setViewPager(viewPager)
    .build()

// 2. 启用插件
plugin.enable()

// 3. 在Activity销毁时释放资源
override fun onDestroy() {
    super.onDestroy()
    plugin.destroy()
}
```

### CoIndicator专用工厂方法

```kotlin
// 使用工厂方法快速创建CoIndicator插件
val plugin = StickyBottomTabPluginFactory.createForCoIndicator(
    context = this,
    coordinatorLayout = coordinatorLayout,
    appBarLayout = appBarLayout,
    coIndicator = coIndicator,
    viewPager = viewPager,
    tabTitles = listOf("推荐", "关注", "热门", "视频"),
    animationDuration = 300L,
    scrollToPosition = 0.33f
)

plugin.enable()
```

## 📋 API文档

### StickyBottomTabPlugin.Builder

| 方法 | 参数 | 说明 | 必需 |
|------|------|------|------|
| `setCoordinatorLayout()` | CoordinatorLayout | 设置CoordinatorLayout容器 | ✅ |
| `setAppBarLayout()` | AppBarLayout | 设置AppBarLayout | ✅ |
| `setOriginalTabLayout()` | ViewGroup | 设置原始Tab布局 | ✅ |
| `setViewPager()` | ViewPager2? | 设置ViewPager2 | ❌ |
| `setAnimationDuration()` | Long | 设置动画时长(ms)，默认300 | ❌ |
| `setScrollToPosition()` | Float | 设置滚动位置比例，默认0.33f | ❌ |
| `setTabBackgroundColor()` | Int | 设置Tab背景色 | ❌ |
| `setTabElevation()` | Float | 设置Tab阴影高度 | ❌ |
| `setOnTabClickListener()` | (Int) -> Unit | 设置Tab点击监听器 | ❌ |

### StickyBottomTabPlugin

| 方法 | 说明 |
|------|------|
| `enable()` | 启用插件 |
| `disable()` | 禁用插件 |
| `isEnabled()` | 检查插件是否已启用 |
| `isBottomTabVisible()` | 检查吸底Tab是否正在显示 |
| `destroy()` | 销毁插件，释放所有资源 |

### StickyBottomTabPluginFactory

| 方法 | 说明 |
|------|------|
| `createForCoIndicator()` | 为CoIndicator创建专用插件 |

## 🎨 自定义配置

### 动画配置

```kotlin
val plugin = StickyBottomTabPlugin.Builder(context)
    .setAnimationDuration(500L)  // 设置动画时长为500ms
    .build()
```

### 样式配置

```kotlin
val plugin = StickyBottomTabPlugin.Builder(context)
    .setTabBackgroundColor(Color.WHITE)  // 设置背景色
    .setTabElevation(12f)               // 设置阴影高度
    .build()
```

### 交互配置

```kotlin
val plugin = StickyBottomTabPlugin.Builder(context)
    .setScrollToPosition(0.25f)  // 点击后滚动到屏幕1/4位置
    .setOnTabClickListener { position ->
        // 自定义点击处理逻辑
        Log.d("Tab", "点击了第 $position 个Tab")
    }
    .build()
```

## 🔧 工作原理

1. **位置检测**：通过`getLocationOnScreen()`实时检测原始Tab的屏幕位置
2. **状态判断**：当Tab滚出屏幕底部时触发显示逻辑
3. **动态创建**：创建与原始Tab相同的吸底Tab组件
4. **动画展示**：使用属性动画实现流畅的滑入效果
5. **状态同步**：通过ViewPager2的页面变化监听保持状态同步
6. **交互处理**：点击吸底Tab后执行页面切换和滚动动画
7. **资源管理**：自动处理组件的创建、销毁和内存释放

## 📱 兼容性

- **最低API级别**：API 21 (Android 5.0)
- **支持的Tab组件**：TabLayout、CoIndicator、自定义Tab组件
- **支持的容器**：CoordinatorLayout + AppBarLayout
- **支持的页面组件**：ViewPager2

## 🎯 最佳实践

1. **及时销毁**：在Activity/Fragment的`onDestroy()`中调用`plugin.destroy()`
2. **合理配置**：根据实际需求调整动画时长和滚动位置
3. **性能优化**：避免在短时间内频繁启用/禁用插件
4. **错误处理**：在插件操作前检查必要的View是否已初始化

## 🐛 常见问题

**Q: 插件不工作怎么办？**
A: 检查CoordinatorLayout、AppBarLayout和原始Tab是否正确设置，确保调用了`enable()`方法。

**Q: 动画效果不流畅？**
A: 可以尝试调整`animationDuration`参数，或检查是否有其他动画冲突。

**Q: 内存泄漏问题？**
A: 确保在适当的生命周期方法中调用`destroy()`方法释放资源。

## 📄 许可证

本插件遵循项目的整体许可证协议。
