  ContentFragment android.app.Activity  Int android.app.Activity  IntArray android.app.Activity  LayoutInflater android.app.Activity  LinearLayout android.app.Activity  R android.app.Activity  RelativeLayout android.app.Activity  SmartRefreshLayout android.app.Activity  	TabLayout android.app.Activity  TabLayoutMediator android.app.Activity  TabPagerAdapter android.app.Activity  TextView android.app.Activity  View android.app.Activity  
ViewPager2 android.app.Activity  ViewTreeObserver android.app.Activity  coordinatorLayout android.app.Activity  currentTabPosition android.app.Activity  indices android.app.Activity  layoutInflater android.app.Activity  let android.app.Activity  listOf android.app.Activity  maxOf android.app.Activity  measureHeightsAndSetupSticky android.app.Activity  newInstance android.app.Activity  onCreate android.app.Activity  scrollToOneThirdPosition android.app.Activity  	tabLayout android.app.Activity  updateFloatingTabSelection android.app.Activity  OnTabSelectedListener android.app.Activity.TabLayout  Tab android.app.Activity.TabLayout  OnPageChangeCallback android.app.Activity.ViewPager2  OnGlobalLayoutListener %android.app.Activity.ViewTreeObserver  Context android.content  ContentFragment android.content.Context  Int android.content.Context  IntArray android.content.Context  LayoutInflater android.content.Context  LinearLayout android.content.Context  R android.content.Context  RelativeLayout android.content.Context  SmartRefreshLayout android.content.Context  	TabLayout android.content.Context  TabLayoutMediator android.content.Context  TabPagerAdapter android.content.Context  TextView android.content.Context  View android.content.Context  
ViewPager2 android.content.Context  ViewTreeObserver android.content.Context  coordinatorLayout android.content.Context  currentTabPosition android.content.Context  indices android.content.Context  let android.content.Context  listOf android.content.Context  maxOf android.content.Context  measureHeightsAndSetupSticky android.content.Context  newInstance android.content.Context  obtainStyledAttributes android.content.Context  scrollToOneThirdPosition android.content.Context  	tabLayout android.content.Context  updateFloatingTabSelection android.content.Context  OnTabSelectedListener !android.content.Context.TabLayout  Tab !android.content.Context.TabLayout  OnPageChangeCallback "android.content.Context.ViewPager2  OnGlobalLayoutListener (android.content.Context.ViewTreeObserver  ContentFragment android.content.ContextWrapper  Int android.content.ContextWrapper  IntArray android.content.ContextWrapper  LayoutInflater android.content.ContextWrapper  LinearLayout android.content.ContextWrapper  R android.content.ContextWrapper  RelativeLayout android.content.ContextWrapper  SmartRefreshLayout android.content.ContextWrapper  	TabLayout android.content.ContextWrapper  TabLayoutMediator android.content.ContextWrapper  TabPagerAdapter android.content.ContextWrapper  TextView android.content.ContextWrapper  View android.content.ContextWrapper  
ViewPager2 android.content.ContextWrapper  ViewTreeObserver android.content.ContextWrapper  coordinatorLayout android.content.ContextWrapper  currentTabPosition android.content.ContextWrapper  indices android.content.ContextWrapper  let android.content.ContextWrapper  listOf android.content.ContextWrapper  maxOf android.content.ContextWrapper  measureHeightsAndSetupSticky android.content.ContextWrapper  newInstance android.content.ContextWrapper  scrollToOneThirdPosition android.content.ContextWrapper  	tabLayout android.content.ContextWrapper  updateFloatingTabSelection android.content.ContextWrapper  OnTabSelectedListener (android.content.ContextWrapper.TabLayout  Tab (android.content.ContextWrapper.TabLayout  OnPageChangeCallback )android.content.ContextWrapper.ViewPager2  OnGlobalLayoutListener /android.content.ContextWrapper.ViewTreeObserver  
TypedArray android.content.res  displayMetrics android.content.res.Resources  
getBoolean android.content.res.TypedArray  getColor android.content.res.TypedArray  getDimension android.content.res.TypedArray  recycle android.content.res.TypedArray  Canvas android.graphics  Paint android.graphics  Path android.graphics  RectF android.graphics  clipPath android.graphics.Canvas  
drawRoundRect android.graphics.Canvas  ANTI_ALIAS_FLAG android.graphics.Paint  Style android.graphics.Paint  color android.graphics.Paint  style android.graphics.Paint  FILL android.graphics.Paint.Style  	Direction android.graphics.Path  addRoundRect android.graphics.Path  reset android.graphics.Path  CW android.graphics.Path.Direction  set android.graphics.RectF  Bundle 
android.os  getInt android.os.BaseBundle  	getString android.os.BaseBundle  putInt android.os.BaseBundle  	putString android.os.BaseBundle  getInt android.os.Bundle  	getString android.os.Bundle  let android.os.Bundle  putInt android.os.Bundle  	putString android.os.Bundle  AttributeSet android.util  let android.util.AttributeSet  heightPixels android.util.DisplayMetrics  LayoutInflater android.view  View android.view  	ViewGroup android.view  ViewPropertyAnimator android.view  ViewTreeObserver android.view  ContentFragment  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  IntArray  android.view.ContextThemeWrapper  LayoutInflater  android.view.ContextThemeWrapper  LinearLayout  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  RelativeLayout  android.view.ContextThemeWrapper  SmartRefreshLayout  android.view.ContextThemeWrapper  	TabLayout  android.view.ContextThemeWrapper  TabLayoutMediator  android.view.ContextThemeWrapper  TabPagerAdapter  android.view.ContextThemeWrapper  TextView  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  
ViewPager2  android.view.ContextThemeWrapper  ViewTreeObserver  android.view.ContextThemeWrapper  coordinatorLayout  android.view.ContextThemeWrapper  currentTabPosition  android.view.ContextThemeWrapper  indices  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  maxOf  android.view.ContextThemeWrapper  measureHeightsAndSetupSticky  android.view.ContextThemeWrapper  newInstance  android.view.ContextThemeWrapper  scrollToOneThirdPosition  android.view.ContextThemeWrapper  	tabLayout  android.view.ContextThemeWrapper  updateFloatingTabSelection  android.view.ContextThemeWrapper  OnTabSelectedListener *android.view.ContextThemeWrapper.TabLayout  Tab *android.view.ContextThemeWrapper.TabLayout  OnPageChangeCallback +android.view.ContextThemeWrapper.ViewPager2  OnGlobalLayoutListener 1android.view.ContextThemeWrapper.ViewTreeObserver  from android.view.LayoutInflater  inflate android.view.LayoutInflater  GONE android.view.View  
GRAVITY_START android.view.View  LAYER_TYPE_SOFTWARE android.view.View  MODE_SCROLLABLE android.view.View  OnScrollChangeListener android.view.View  OnTabSelectedListener android.view.View  Paint android.view.View  Path android.view.View  R android.view.View  RectF android.view.View  Tab android.view.View  VISIBLE android.view.View  alpha android.view.View  animate android.view.View  findViewById android.view.View  getLocationOnScreen android.view.View  height android.view.View  layoutParams android.view.View  let android.view.View  post android.view.View  postDelayed android.view.View  setLayerType android.view.View  setOnScrollChangeListener android.view.View  viewTreeObserver android.view.View  
visibility android.view.View  width android.view.View  <SAM-CONSTRUCTOR> (android.view.View.OnScrollChangeListener  
GRAVITY_START android.view.ViewGroup  LAYER_TYPE_SOFTWARE android.view.ViewGroup  MODE_SCROLLABLE android.view.ViewGroup  OnTabSelectedListener android.view.ViewGroup  Paint android.view.ViewGroup  Path android.view.ViewGroup  R android.view.ViewGroup  RectF android.view.ViewGroup  Tab android.view.ViewGroup  addView android.view.ViewGroup  let android.view.ViewGroup  MATCH_PARENT #android.view.ViewGroup.LayoutParams  WRAP_CONTENT #android.view.ViewGroup.LayoutParams  alpha !android.view.ViewPropertyAnimator  setDuration !android.view.ViewPropertyAnimator  start !android.view.ViewPropertyAnimator  
withEndAction !android.view.ViewPropertyAnimator  OnGlobalLayoutListener android.view.ViewTreeObserver  addOnGlobalLayoutListener android.view.ViewTreeObserver  removeOnGlobalLayoutListener android.view.ViewTreeObserver  LinearLayout android.widget  RelativeLayout android.widget  TextView android.widget  
GRAVITY_START android.widget.FrameLayout  MODE_SCROLLABLE android.widget.FrameLayout  OnTabSelectedListener android.widget.FrameLayout  Tab android.widget.FrameLayout  let android.widget.FrameLayout  
GRAVITY_START #android.widget.HorizontalScrollView  MODE_SCROLLABLE #android.widget.HorizontalScrollView  OnTabSelectedListener #android.widget.HorizontalScrollView  Tab #android.widget.HorizontalScrollView  let #android.widget.HorizontalScrollView  LAYER_TYPE_SOFTWARE android.widget.LinearLayout  Paint android.widget.LinearLayout  Path android.widget.LinearLayout  R android.widget.LinearLayout  RectF android.widget.LinearLayout  addView android.widget.LinearLayout  alpha android.widget.LinearLayout  animate android.widget.LinearLayout  getLocationOnScreen android.widget.LinearLayout  height android.widget.LinearLayout  layoutParams android.widget.LinearLayout  let android.widget.LinearLayout  onDraw android.widget.LinearLayout  
visibility android.widget.LinearLayout  ALIGN_PARENT_TOP android.widget.RelativeLayout  LayoutParams android.widget.RelativeLayout  addView android.widget.RelativeLayout  MATCH_PARENT *android.widget.RelativeLayout.LayoutParams  WRAP_CONTENT *android.widget.RelativeLayout.LayoutParams  addRule *android.widget.RelativeLayout.LayoutParams  text android.widget.TextView  ContentFragment #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  IntArray #androidx.activity.ComponentActivity  LayoutInflater #androidx.activity.ComponentActivity  LinearLayout #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  RelativeLayout #androidx.activity.ComponentActivity  SmartRefreshLayout #androidx.activity.ComponentActivity  	TabLayout #androidx.activity.ComponentActivity  TabLayoutMediator #androidx.activity.ComponentActivity  TabPagerAdapter #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  
ViewPager2 #androidx.activity.ComponentActivity  ViewTreeObserver #androidx.activity.ComponentActivity  coordinatorLayout #androidx.activity.ComponentActivity  currentTabPosition #androidx.activity.ComponentActivity  indices #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  listOf #androidx.activity.ComponentActivity  maxOf #androidx.activity.ComponentActivity  measureHeightsAndSetupSticky #androidx.activity.ComponentActivity  newInstance #androidx.activity.ComponentActivity  scrollToOneThirdPosition #androidx.activity.ComponentActivity  	tabLayout #androidx.activity.ComponentActivity  updateFloatingTabSelection #androidx.activity.ComponentActivity  OnTabSelectedListener -androidx.activity.ComponentActivity.TabLayout  Tab -androidx.activity.ComponentActivity.TabLayout  OnPageChangeCallback .androidx.activity.ComponentActivity.ViewPager2  OnGlobalLayoutListener 4androidx.activity.ComponentActivity.ViewTreeObserver  AppCompatActivity androidx.appcompat.app  ContentFragment (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  IntArray (androidx.appcompat.app.AppCompatActivity  LayoutInflater (androidx.appcompat.app.AppCompatActivity  LinearLayout (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  RelativeLayout (androidx.appcompat.app.AppCompatActivity  SmartRefreshLayout (androidx.appcompat.app.AppCompatActivity  	TabLayout (androidx.appcompat.app.AppCompatActivity  TabLayoutMediator (androidx.appcompat.app.AppCompatActivity  TabPagerAdapter (androidx.appcompat.app.AppCompatActivity  TextView (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  
ViewPager2 (androidx.appcompat.app.AppCompatActivity  ViewTreeObserver (androidx.appcompat.app.AppCompatActivity  coordinatorLayout (androidx.appcompat.app.AppCompatActivity  currentTabPosition (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  indices (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  listOf (androidx.appcompat.app.AppCompatActivity  maxOf (androidx.appcompat.app.AppCompatActivity  measureHeightsAndSetupSticky (androidx.appcompat.app.AppCompatActivity  newInstance (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	resources (androidx.appcompat.app.AppCompatActivity  scrollToOneThirdPosition (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  	tabLayout (androidx.appcompat.app.AppCompatActivity  updateFloatingTabSelection (androidx.appcompat.app.AppCompatActivity  OnTabSelectedListener 2androidx.appcompat.app.AppCompatActivity.TabLayout  Tab 2androidx.appcompat.app.AppCompatActivity.TabLayout  OnPageChangeCallback 3androidx.appcompat.app.AppCompatActivity.ViewPager2  OnGlobalLayoutListener 9androidx.appcompat.app.AppCompatActivity.ViewTreeObserver  CoordinatorLayout !androidx.coordinatorlayout.widget  height 3androidx.coordinatorlayout.widget.CoordinatorLayout  post 3androidx.coordinatorlayout.widget.CoordinatorLayout  viewTreeObserver 3androidx.coordinatorlayout.widget.CoordinatorLayout  AppBarLayout #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  ContentFragment #androidx.core.app.ComponentActivity  CoordinatorLayout #androidx.core.app.ComponentActivity  Fragment #androidx.core.app.ComponentActivity  FragmentActivity #androidx.core.app.ComponentActivity  FragmentStateAdapter #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  IntArray #androidx.core.app.ComponentActivity  LayoutInflater #androidx.core.app.ComponentActivity  LinearLayout #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  NestedScrollView #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  RelativeLayout #androidx.core.app.ComponentActivity  SmartRefreshLayout #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  	TabLayout #androidx.core.app.ComponentActivity  TabLayoutMediator #androidx.core.app.ComponentActivity  TabPagerAdapter #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  
ViewPager2 #androidx.core.app.ComponentActivity  ViewTreeObserver #androidx.core.app.ComponentActivity  coordinatorLayout #androidx.core.app.ComponentActivity  currentTabPosition #androidx.core.app.ComponentActivity  indices #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  listOf #androidx.core.app.ComponentActivity  maxOf #androidx.core.app.ComponentActivity  measureHeightsAndSetupSticky #androidx.core.app.ComponentActivity  newInstance #androidx.core.app.ComponentActivity  scrollToOneThirdPosition #androidx.core.app.ComponentActivity  	tabLayout #androidx.core.app.ComponentActivity  updateFloatingTabSelection #androidx.core.app.ComponentActivity  OnTabSelectedListener -androidx.core.app.ComponentActivity.TabLayout  Tab -androidx.core.app.ComponentActivity.TabLayout  OnPageChangeCallback .androidx.core.app.ComponentActivity.ViewPager2  OnGlobalLayoutListener 4androidx.core.app.ComponentActivity.ViewTreeObserver  NestedScrollView androidx.core.widget  OnScrollChangeListener %androidx.core.widget.NestedScrollView  setOnScrollChangeListener %androidx.core.widget.NestedScrollView  smoothScrollTo %androidx.core.widget.NestedScrollView  <SAM-CONSTRUCTOR> <androidx.core.widget.NestedScrollView.OnScrollChangeListener  Fragment androidx.fragment.app  FragmentActivity androidx.fragment.app  ARG_POSITION androidx.fragment.app.Fragment  
ARG_TAB_TITLE androidx.fragment.app.Fragment  Bundle androidx.fragment.app.Fragment  ContentFragment androidx.fragment.app.Fragment  R androidx.fragment.app.Fragment  TextView androidx.fragment.app.Fragment  	arguments androidx.fragment.app.Fragment  let androidx.fragment.app.Fragment  onCreate androidx.fragment.app.Fragment  
onViewCreated androidx.fragment.app.Fragment  ContentFragment &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  IntArray &androidx.fragment.app.FragmentActivity  LayoutInflater &androidx.fragment.app.FragmentActivity  LinearLayout &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  RelativeLayout &androidx.fragment.app.FragmentActivity  SmartRefreshLayout &androidx.fragment.app.FragmentActivity  	TabLayout &androidx.fragment.app.FragmentActivity  TabLayoutMediator &androidx.fragment.app.FragmentActivity  TabPagerAdapter &androidx.fragment.app.FragmentActivity  TextView &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  
ViewPager2 &androidx.fragment.app.FragmentActivity  ViewTreeObserver &androidx.fragment.app.FragmentActivity  coordinatorLayout &androidx.fragment.app.FragmentActivity  currentTabPosition &androidx.fragment.app.FragmentActivity  indices &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  listOf &androidx.fragment.app.FragmentActivity  maxOf &androidx.fragment.app.FragmentActivity  measureHeightsAndSetupSticky &androidx.fragment.app.FragmentActivity  newInstance &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  scrollToOneThirdPosition &androidx.fragment.app.FragmentActivity  	tabLayout &androidx.fragment.app.FragmentActivity  updateFloatingTabSelection &androidx.fragment.app.FragmentActivity  OnTabSelectedListener 0androidx.fragment.app.FragmentActivity.TabLayout  Tab 0androidx.fragment.app.FragmentActivity.TabLayout  OnPageChangeCallback 1androidx.fragment.app.FragmentActivity.ViewPager2  OnGlobalLayoutListener 7androidx.fragment.app.FragmentActivity.ViewTreeObserver  ContentFragment 1androidx.recyclerview.widget.RecyclerView.Adapter  newInstance 1androidx.recyclerview.widget.RecyclerView.Adapter  FragmentStateAdapter androidx.viewpager2.adapter  ContentFragment 0androidx.viewpager2.adapter.FragmentStateAdapter  newInstance 0androidx.viewpager2.adapter.FragmentStateAdapter  
ViewPager2 androidx.viewpager2.widget  OnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  adapter %androidx.viewpager2.widget.ViewPager2  offscreenPageLimit %androidx.viewpager2.widget.ViewPager2  registerOnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  currentTabPosition :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  onPageSelected :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  scrollToOneThirdPosition :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  updateFloatingTabSelection :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  AppBarLayout "com.google.android.material.appbar  Behavior /com.google.android.material.appbar.AppBarLayout  height /com.google.android.material.appbar.AppBarLayout  onNestedPreScroll 8com.google.android.material.appbar.AppBarLayout.Behavior  onNestedScroll 8com.google.android.material.appbar.AppBarLayout.Behavior  	TabLayout  com.google.android.material.tabs  TabLayoutMediator  com.google.android.material.tabs  
GRAVITY_START *com.google.android.material.tabs.TabLayout  MODE_SCROLLABLE *com.google.android.material.tabs.TabLayout  OnTabSelectedListener *com.google.android.material.tabs.TabLayout  Tab *com.google.android.material.tabs.TabLayout  addOnTabSelectedListener *com.google.android.material.tabs.TabLayout  addTab *com.google.android.material.tabs.TabLayout  let *com.google.android.material.tabs.TabLayout  newTab *com.google.android.material.tabs.TabLayout  postDelayed *com.google.android.material.tabs.TabLayout  
tabGravity *com.google.android.material.tabs.TabLayout  tabMode *com.google.android.material.tabs.TabLayout  let .com.google.android.material.tabs.TabLayout.Tab  position .com.google.android.material.tabs.TabLayout.Tab  text .com.google.android.material.tabs.TabLayout.Tab  TabConfigurationStrategy 2com.google.android.material.tabs.TabLayoutMediator  attach 2com.google.android.material.tabs.TabLayoutMediator  <SAM-CONSTRUCTOR> Kcom.google.android.material.tabs.TabLayoutMediator.TabConfigurationStrategy  AttributeSet com.hbg.lib.widgets  Context com.hbg.lib.widgets  Int com.hbg.lib.widgets  JvmOverloads com.hbg.lib.widgets  LoadingRelativeLayout com.hbg.lib.widgets  RelativeLayout com.hbg.lib.widgets  AppBarLayout !com.hbg.module.libkt.custom.coord  AppBarLayoutBehavior !com.hbg.module.libkt.custom.coord  AttributeSet !com.hbg.module.libkt.custom.coord  Context !com.hbg.module.libkt.custom.coord  CoordinatorLayout !com.hbg.module.libkt.custom.coord  Int !com.hbg.module.libkt.custom.coord  IntArray !com.hbg.module.libkt.custom.coord  View !com.hbg.module.libkt.custom.coord  Behavior .com.hbg.module.libkt.custom.coord.AppBarLayout  AttributeSet %com.hbg.module.libkt.custom.indicator  CoIndicator %com.hbg.module.libkt.custom.indicator  Context %com.hbg.module.libkt.custom.indicator  
GRAVITY_START %com.hbg.module.libkt.custom.indicator  Int %com.hbg.module.libkt.custom.indicator  JvmOverloads %com.hbg.module.libkt.custom.indicator  MODE_SCROLLABLE %com.hbg.module.libkt.custom.indicator  OnTabSelectedListener %com.hbg.module.libkt.custom.indicator  String %com.hbg.module.libkt.custom.indicator  Tab %com.hbg.module.libkt.custom.indicator  	TabLayout %com.hbg.module.libkt.custom.indicator  Unit %com.hbg.module.libkt.custom.indicator  let %com.hbg.module.libkt.custom.indicator  
GRAVITY_START 1com.hbg.module.libkt.custom.indicator.CoIndicator  MODE_SCROLLABLE 1com.hbg.module.libkt.custom.indicator.CoIndicator  addOnTabSelectedListener 1com.hbg.module.libkt.custom.indicator.CoIndicator  addTab 1com.hbg.module.libkt.custom.indicator.CoIndicator  let 1com.hbg.module.libkt.custom.indicator.CoIndicator  newTab 1com.hbg.module.libkt.custom.indicator.CoIndicator  
tabGravity 1com.hbg.module.libkt.custom.indicator.CoIndicator  tabMode 1com.hbg.module.libkt.custom.indicator.CoIndicator  AttributeSet com.huobi.view  Context com.huobi.view  Int com.huobi.view  JvmOverloads com.huobi.view  MyNestedScrollView com.huobi.view  NestedScrollView com.huobi.view  AttributeSet com.huobi.view.roundview  Canvas com.huobi.view.roundview  Context com.huobi.view.roundview  Int com.huobi.view.roundview  JvmOverloads com.huobi.view.roundview  LAYER_TYPE_SOFTWARE com.huobi.view.roundview  LinearLayout com.huobi.view.roundview  Paint com.huobi.view.roundview  Path com.huobi.view.roundview  R com.huobi.view.roundview  RectF com.huobi.view.roundview  RoundLinearLayout com.huobi.view.roundview  let com.huobi.view.roundview  LAYER_TYPE_SOFTWARE *com.huobi.view.roundview.RoundLinearLayout  Paint *com.huobi.view.roundview.RoundLinearLayout  Path *com.huobi.view.roundview.RoundLinearLayout  R *com.huobi.view.roundview.RoundLinearLayout  RectF *com.huobi.view.roundview.RoundLinearLayout  backgroundColor *com.huobi.view.roundview.RoundLinearLayout  cornerRadius *com.huobi.view.roundview.RoundLinearLayout  height *com.huobi.view.roundview.RoundLinearLayout  isRippleEnable *com.huobi.view.roundview.RoundLinearLayout  let *com.huobi.view.roundview.RoundLinearLayout  paint *com.huobi.view.roundview.RoundLinearLayout  path *com.huobi.view.roundview.RoundLinearLayout  rectF *com.huobi.view.roundview.RoundLinearLayout  setLayerType *com.huobi.view.roundview.RoundLinearLayout  width *com.huobi.view.roundview.RoundLinearLayout  SmartRefreshLayout com.scwang.smart.refresh.layout  ARG_POSITION com.ttv.demo  
ARG_TAB_TITLE com.ttv.demo  AppBarLayout com.ttv.demo  AppCompatActivity com.ttv.demo  AttributeSet com.ttv.demo  Bundle com.ttv.demo  ContentFragment com.ttv.demo  Context com.ttv.demo  CoordinatorLayout com.ttv.demo  CustomAppBarLayoutBehavior com.ttv.demo  Fragment com.ttv.demo  FragmentActivity com.ttv.demo  FragmentStateAdapter com.ttv.demo  Int com.ttv.demo  IntArray com.ttv.demo  LayoutInflater com.ttv.demo  LinearLayout com.ttv.demo  List com.ttv.demo  MainActivity com.ttv.demo  NestedScrollView com.ttv.demo  R com.ttv.demo  RelativeLayout com.ttv.demo  SmartRefreshLayout com.ttv.demo  StickyTabActivity com.ttv.demo  String com.ttv.demo  	TabLayout com.ttv.demo  TabLayoutMediator com.ttv.demo  TabPagerAdapter com.ttv.demo  TextView com.ttv.demo  View com.ttv.demo  	ViewGroup com.ttv.demo  
ViewPager2 com.ttv.demo  ViewTreeObserver com.ttv.demo  coordinatorLayout com.ttv.demo  currentTabPosition com.ttv.demo  indices com.ttv.demo  let com.ttv.demo  listOf com.ttv.demo  maxOf com.ttv.demo  measureHeightsAndSetupSticky com.ttv.demo  newInstance com.ttv.demo  scrollToOneThirdPosition com.ttv.demo  	tabLayout com.ttv.demo  updateFloatingTabSelection com.ttv.demo  Behavior com.ttv.demo.AppBarLayout  ARG_POSITION com.ttv.demo.ContentFragment  
ARG_TAB_TITLE com.ttv.demo.ContentFragment  Bundle com.ttv.demo.ContentFragment  	Companion com.ttv.demo.ContentFragment  ContentFragment com.ttv.demo.ContentFragment  Int com.ttv.demo.ContentFragment  LayoutInflater com.ttv.demo.ContentFragment  R com.ttv.demo.ContentFragment  String com.ttv.demo.ContentFragment  TextView com.ttv.demo.ContentFragment  View com.ttv.demo.ContentFragment  	ViewGroup com.ttv.demo.ContentFragment  	arguments com.ttv.demo.ContentFragment  let com.ttv.demo.ContentFragment  newInstance com.ttv.demo.ContentFragment  position com.ttv.demo.ContentFragment  setupContentForTab com.ttv.demo.ContentFragment  tabTitle com.ttv.demo.ContentFragment  ARG_POSITION &com.ttv.demo.ContentFragment.Companion  
ARG_TAB_TITLE &com.ttv.demo.ContentFragment.Companion  Bundle &com.ttv.demo.ContentFragment.Companion  ContentFragment &com.ttv.demo.ContentFragment.Companion  R &com.ttv.demo.ContentFragment.Companion  let &com.ttv.demo.ContentFragment.Companion  newInstance &com.ttv.demo.ContentFragment.Companion  AppBarLayout com.ttv.demo.MainActivity  Bundle com.ttv.demo.MainActivity  ContentFragment com.ttv.demo.MainActivity  CoordinatorLayout com.ttv.demo.MainActivity  Fragment com.ttv.demo.MainActivity  FragmentActivity com.ttv.demo.MainActivity  FragmentStateAdapter com.ttv.demo.MainActivity  Int com.ttv.demo.MainActivity  LinearLayout com.ttv.demo.MainActivity  List com.ttv.demo.MainActivity  NestedScrollView com.ttv.demo.MainActivity  R com.ttv.demo.MainActivity  String com.ttv.demo.MainActivity  	TabLayout com.ttv.demo.MainActivity  TabLayoutMediator com.ttv.demo.MainActivity  TabPagerAdapter com.ttv.demo.MainActivity  TextView com.ttv.demo.MainActivity  
ViewPager2 com.ttv.demo.MainActivity  appBarLayout com.ttv.demo.MainActivity  coordinatorLayout com.ttv.demo.MainActivity  findViewById com.ttv.demo.MainActivity  fluentContainer com.ttv.demo.MainActivity  	initViews com.ttv.demo.MainActivity  layoutInflater com.ttv.demo.MainActivity  let com.ttv.demo.MainActivity  listOf com.ttv.demo.MainActivity  nestedScrollView com.ttv.demo.MainActivity  newInstance com.ttv.demo.MainActivity  	resources com.ttv.demo.MainActivity  scrollToOneThirdPosition com.ttv.demo.MainActivity  setContentView com.ttv.demo.MainActivity  setupContent com.ttv.demo.MainActivity  setupTabLayout com.ttv.demo.MainActivity  setupViewPager com.ttv.demo.MainActivity  	tabLayout com.ttv.demo.MainActivity  	tabTitles com.ttv.demo.MainActivity  	viewPager com.ttv.demo.MainActivity  OnTabSelectedListener #com.ttv.demo.MainActivity.TabLayout  Tab #com.ttv.demo.MainActivity.TabLayout  ContentFragment )com.ttv.demo.MainActivity.TabPagerAdapter  newInstance )com.ttv.demo.MainActivity.TabPagerAdapter  	tabTitles )com.ttv.demo.MainActivity.TabPagerAdapter  appBarLayout com.ttv.demo.R.id  clLayout com.ttv.demo.R.id  coIndicator com.ttv.demo.R.id  fluent_container com.ttv.demo.R.id  fluent_content_nsv com.ttv.demo.R.id  fluent_refresh_layout com.ttv.demo.R.id  home_feed_linear_tabLayout com.ttv.demo.R.id  home_viewPager com.ttv.demo.R.id   rl_new_hand_area_animation_layer com.ttv.demo.R.id  tv_card_content com.ttv.demo.R.id  
tv_card_title com.ttv.demo.R.id  tv_tab_title com.ttv.demo.R.id  
activity_main com.ttv.demo.R.layout  content_card com.ttv.demo.R.layout  floating_tab_layout com.ttv.demo.R.layout  fragment_content com.ttv.demo.R.layout  RoundLinearLayout com.ttv.demo.R.styleable  $RoundLinearLayout_rv_backgroundColor com.ttv.demo.R.styleable  !RoundLinearLayout_rv_cornerRadius com.ttv.demo.R.styleable  #RoundLinearLayout_rv_isRippleEnable com.ttv.demo.R.styleable  AppBarLayout com.ttv.demo.StickyTabActivity  Bundle com.ttv.demo.StickyTabActivity  ContentFragment com.ttv.demo.StickyTabActivity  CoordinatorLayout com.ttv.demo.StickyTabActivity  Fragment com.ttv.demo.StickyTabActivity  FragmentActivity com.ttv.demo.StickyTabActivity  FragmentStateAdapter com.ttv.demo.StickyTabActivity  Int com.ttv.demo.StickyTabActivity  IntArray com.ttv.demo.StickyTabActivity  LayoutInflater com.ttv.demo.StickyTabActivity  LinearLayout com.ttv.demo.StickyTabActivity  List com.ttv.demo.StickyTabActivity  NestedScrollView com.ttv.demo.StickyTabActivity  R com.ttv.demo.StickyTabActivity  RelativeLayout com.ttv.demo.StickyTabActivity  SmartRefreshLayout com.ttv.demo.StickyTabActivity  String com.ttv.demo.StickyTabActivity  TabPagerAdapter com.ttv.demo.StickyTabActivity  TextView com.ttv.demo.StickyTabActivity  View com.ttv.demo.StickyTabActivity  
ViewPager2 com.ttv.demo.StickyTabActivity  ViewTreeObserver com.ttv.demo.StickyTabActivity  animationLayer com.ttv.demo.StickyTabActivity  appBarLayout com.ttv.demo.StickyTabActivity  availableHeight com.ttv.demo.StickyTabActivity  
contentHeight com.ttv.demo.StickyTabActivity  coordinatorLayout com.ttv.demo.StickyTabActivity  createFloatingTab com.ttv.demo.StickyTabActivity  currentTabPosition com.ttv.demo.StickyTabActivity  findViewById com.ttv.demo.StickyTabActivity  floatingTabLayout com.ttv.demo.StickyTabActivity  fluentContainer com.ttv.demo.StickyTabActivity  indices com.ttv.demo.StickyTabActivity  	initViews com.ttv.demo.StickyTabActivity  let com.ttv.demo.StickyTabActivity  listOf com.ttv.demo.StickyTabActivity  maxOf com.ttv.demo.StickyTabActivity  measureHeightsAndSetupSticky com.ttv.demo.StickyTabActivity  nestedScrollView com.ttv.demo.StickyTabActivity  newInstance com.ttv.demo.StickyTabActivity  originalTabLayout com.ttv.demo.StickyTabActivity  	resources com.ttv.demo.StickyTabActivity  screenHeight com.ttv.demo.StickyTabActivity  scrollToOneThirdPosition com.ttv.demo.StickyTabActivity  setContentView com.ttv.demo.StickyTabActivity  setupContent com.ttv.demo.StickyTabActivity  setupFloatingTabClickListeners com.ttv.demo.StickyTabActivity  setupScrollListener com.ttv.demo.StickyTabActivity  setupStickyTabLogic com.ttv.demo.StickyTabActivity  setupViewPager com.ttv.demo.StickyTabActivity  smartRefreshLayout com.ttv.demo.StickyTabActivity  tabLayoutTop com.ttv.demo.StickyTabActivity  	tabTitles com.ttv.demo.StickyTabActivity  updateFloatingTabSelection com.ttv.demo.StickyTabActivity  	viewPager com.ttv.demo.StickyTabActivity  ContentFragment .com.ttv.demo.StickyTabActivity.TabPagerAdapter  newInstance .com.ttv.demo.StickyTabActivity.TabPagerAdapter  	tabTitles .com.ttv.demo.StickyTabActivity.TabPagerAdapter  OnPageChangeCallback )com.ttv.demo.StickyTabActivity.ViewPager2  OnGlobalLayoutListener /com.ttv.demo.StickyTabActivity.ViewTreeObserver  OnTabSelectedListener com.ttv.demo.TabLayout  Tab com.ttv.demo.TabLayout  OnPageChangeCallback com.ttv.demo.ViewPager2  OnGlobalLayoutListener com.ttv.demo.ViewTreeObserver  Runnable 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function5 kotlin  IntArray kotlin  Nothing kotlin  let kotlin  not kotlin.Boolean  	compareTo kotlin.Float  invoke kotlin.Function1  	compareTo 
kotlin.Int  div 
kotlin.Int  minus 
kotlin.Int  rangeTo 
kotlin.Int  toFloat 
kotlin.Int  get kotlin.IntArray  IntIterator kotlin.collections  List kotlin.collections  indices kotlin.collections  listOf kotlin.collections  maxOf kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  get kotlin.collections.List  indices kotlin.collections.List  size kotlin.collections.List  maxOf kotlin.comparisons  JvmOverloads 
kotlin.jvm  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  maxOf kotlin.sequences  indices kotlin.text  maxOf kotlin.text  AppBarLayout android.app.Activity  CoordinatorLayout android.app.Activity  NestedScrollView android.app.Activity  AppBarLayout android.content.Context  CoordinatorLayout android.content.Context  NestedScrollView android.content.Context  AppBarLayout android.content.ContextWrapper  CoordinatorLayout android.content.ContextWrapper  NestedScrollView android.content.ContextWrapper  AppBarLayout  android.view.ContextThemeWrapper  CoordinatorLayout  android.view.ContextThemeWrapper  NestedScrollView  android.view.ContextThemeWrapper  AppBarLayout #androidx.activity.ComponentActivity  CoordinatorLayout #androidx.activity.ComponentActivity  NestedScrollView #androidx.activity.ComponentActivity  AppBarLayout (androidx.appcompat.app.AppCompatActivity  CoordinatorLayout (androidx.appcompat.app.AppCompatActivity  NestedScrollView (androidx.appcompat.app.AppCompatActivity  AppBarLayout &androidx.fragment.app.FragmentActivity  CoordinatorLayout &androidx.fragment.app.FragmentActivity  NestedScrollView &androidx.fragment.app.FragmentActivity  Animator android.animation  AnimatorListenerAdapter android.animation  
ValueAnimator android.animation  addListener android.animation.Animator  AnimatorUpdateListener android.animation.ValueAnimator  addUpdateListener android.animation.ValueAnimator  
animatedValue android.animation.ValueAnimator  cancel android.animation.ValueAnimator  duration android.animation.ValueAnimator  ofFloat android.animation.ValueAnimator  start android.animation.ValueAnimator  <SAM-CONSTRUCTOR> 6android.animation.ValueAnimator.AnimatorUpdateListener  StickyBottomTabBehavior android.app.Activity  Behavior !android.app.Activity.AppBarLayout  LayoutParams &android.app.Activity.CoordinatorLayout  StickyBottomTabBehavior android.content.Context  Behavior $android.content.Context.AppBarLayout  LayoutParams )android.content.Context.CoordinatorLayout  StickyBottomTabBehavior android.content.ContextWrapper  Behavior +android.content.ContextWrapper.AppBarLayout  LayoutParams 0android.content.ContextWrapper.CoordinatorLayout  StickyBottomTabBehavior  android.view.ContextThemeWrapper  Behavior -android.view.ContextThemeWrapper.AppBarLayout  LayoutParams 2android.view.ContextThemeWrapper.CoordinatorLayout  BOTTOM android.view.Gravity  
NO_GRAVITY android.view.Gravity  NO_ID android.view.View  y android.view.View  StickyBottomTabBehavior #androidx.activity.ComponentActivity  Behavior 0androidx.activity.ComponentActivity.AppBarLayout  LayoutParams 5androidx.activity.ComponentActivity.CoordinatorLayout  StickyBottomTabBehavior (androidx.appcompat.app.AppCompatActivity  Behavior 5androidx.appcompat.app.AppCompatActivity.AppBarLayout  LayoutParams :androidx.appcompat.app.AppCompatActivity.CoordinatorLayout  Behavior 3androidx.coordinatorlayout.widget.CoordinatorLayout  LayoutParams 3androidx.coordinatorlayout.widget.CoordinatorLayout  postDelayed 3androidx.coordinatorlayout.widget.CoordinatorLayout  AppBarLayout <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  CoordinatorLayout <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  Float <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  
ValueAnimator <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  View <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  android <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  apply <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  onNestedPreScroll <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  LayoutParams Nandroidx.coordinatorlayout.widget.CoordinatorLayout.Behavior.CoordinatorLayout  	animation Dandroidx.coordinatorlayout.widget.CoordinatorLayout.Behavior.android  Animator Nandroidx.coordinatorlayout.widget.CoordinatorLayout.Behavior.android.animation  AnimatorListenerAdapter Nandroidx.coordinatorlayout.widget.CoordinatorLayout.Behavior.android.animation  
anchorGravity @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  anchorId @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  behavior @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  gravity @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  CoIndicator #androidx.core.app.ComponentActivity  StickyBottomTabBehavior #androidx.core.app.ComponentActivity  Behavior 0androidx.core.app.ComponentActivity.AppBarLayout  LayoutParams 5androidx.core.app.ComponentActivity.CoordinatorLayout  
ViewCompat androidx.core.view  StickyBottomTabBehavior &androidx.fragment.app.FragmentActivity  Behavior 3androidx.fragment.app.FragmentActivity.AppBarLayout  LayoutParams 8androidx.fragment.app.FragmentActivity.CoordinatorLayout  layoutParams /com.google.android.material.appbar.AppBarLayout  setExpanded /com.google.android.material.appbar.AppBarLayout  totalScrollRange /com.google.android.material.appbar.AppBarLayout  y /com.google.android.material.appbar.AppBarLayout  topAndBottomOffset 8com.google.android.material.appbar.AppBarLayout.Behavior  topAndBottomOffset 5com.google.android.material.appbar.ViewOffsetBehavior  setOnTabSelectedListener *com.google.android.material.tabs.TabLayout  setOnTabSelectedListener 1com.hbg.module.libkt.custom.indicator.CoIndicator  Boolean com.ttv.demo  CoIndicator com.ttv.demo  Float com.ttv.demo  StickyBottomTabBehavior com.ttv.demo  StickyBottomTabDemoActivity com.ttv.demo  Unit com.ttv.demo  
ValueAnimator com.ttv.demo  android com.ttv.demo  apply com.ttv.demo  forEach com.ttv.demo  Behavior com.ttv.demo.CoordinatorLayout  LayoutParams com.ttv.demo.CoordinatorLayout  CoIndicator com.ttv.demo.MainActivity  StickyBottomTabBehavior com.ttv.demo.MainActivity  coIndicator com.ttv.demo.MainActivity  setupStickyBottomBehavior com.ttv.demo.MainActivity  tabLayoutContainer com.ttv.demo.MainActivity  LayoutParams +com.ttv.demo.MainActivity.CoordinatorLayout  
ValueAnimator $com.ttv.demo.StickyBottomTabBehavior  View $com.ttv.demo.StickyBottomTabBehavior  android $com.ttv.demo.StickyBottomTabBehavior  animateTabPosition $com.ttv.demo.StickyBottomTabBehavior  animator $com.ttv.demo.StickyBottomTabBehavior  apply $com.ttv.demo.StickyBottomTabBehavior  handleTabPosition $com.ttv.demo.StickyBottomTabBehavior  isStickToBottom $com.ttv.demo.StickyBottomTabBehavior  moveTabToBottom $com.ttv.demo.StickyBottomTabBehavior  moveTabToOriginalPosition $com.ttv.demo.StickyBottomTabBehavior  	originalY $com.ttv.demo.StickyBottomTabBehavior  screenHeight $com.ttv.demo.StickyBottomTabBehavior  	tabHeight $com.ttv.demo.StickyBottomTabBehavior  AppBarLayout (com.ttv.demo.StickyBottomTabDemoActivity  Bundle (com.ttv.demo.StickyBottomTabDemoActivity  CoIndicator (com.ttv.demo.StickyBottomTabDemoActivity  ContentFragment (com.ttv.demo.StickyBottomTabDemoActivity  CoordinatorLayout (com.ttv.demo.StickyBottomTabDemoActivity  Fragment (com.ttv.demo.StickyBottomTabDemoActivity  FragmentActivity (com.ttv.demo.StickyBottomTabDemoActivity  FragmentStateAdapter (com.ttv.demo.StickyBottomTabDemoActivity  Int (com.ttv.demo.StickyBottomTabDemoActivity  LayoutInflater (com.ttv.demo.StickyBottomTabDemoActivity  LinearLayout (com.ttv.demo.StickyBottomTabDemoActivity  List (com.ttv.demo.StickyBottomTabDemoActivity  NestedScrollView (com.ttv.demo.StickyBottomTabDemoActivity  R (com.ttv.demo.StickyBottomTabDemoActivity  StickyBottomTabBehavior (com.ttv.demo.StickyBottomTabDemoActivity  String (com.ttv.demo.StickyBottomTabDemoActivity  TabLayoutMediator (com.ttv.demo.StickyBottomTabDemoActivity  TabPagerAdapter (com.ttv.demo.StickyBottomTabDemoActivity  TextView (com.ttv.demo.StickyBottomTabDemoActivity  
ViewPager2 (com.ttv.demo.StickyBottomTabDemoActivity  appBarLayout (com.ttv.demo.StickyBottomTabDemoActivity  coIndicator (com.ttv.demo.StickyBottomTabDemoActivity  coordinatorLayout (com.ttv.demo.StickyBottomTabDemoActivity  findViewById (com.ttv.demo.StickyBottomTabDemoActivity  fluentContainer (com.ttv.demo.StickyBottomTabDemoActivity  	initViews (com.ttv.demo.StickyBottomTabDemoActivity  listOf (com.ttv.demo.StickyBottomTabDemoActivity  nestedScrollView (com.ttv.demo.StickyBottomTabDemoActivity  newInstance (com.ttv.demo.StickyBottomTabDemoActivity  	resources (com.ttv.demo.StickyBottomTabDemoActivity  scrollToOneThirdPosition (com.ttv.demo.StickyBottomTabDemoActivity  setContentView (com.ttv.demo.StickyBottomTabDemoActivity  setupContent (com.ttv.demo.StickyBottomTabDemoActivity  setupStickyBottomBehavior (com.ttv.demo.StickyBottomTabDemoActivity  setupTabLayout (com.ttv.demo.StickyBottomTabDemoActivity  setupViewPager (com.ttv.demo.StickyBottomTabDemoActivity  tabLayoutContainer (com.ttv.demo.StickyBottomTabDemoActivity  	tabTitles (com.ttv.demo.StickyBottomTabDemoActivity  	viewPager (com.ttv.demo.StickyBottomTabDemoActivity  Behavior 5com.ttv.demo.StickyBottomTabDemoActivity.AppBarLayout  LayoutParams :com.ttv.demo.StickyBottomTabDemoActivity.CoordinatorLayout  ContentFragment 8com.ttv.demo.StickyBottomTabDemoActivity.TabPagerAdapter  newInstance 8com.ttv.demo.StickyBottomTabDemoActivity.TabPagerAdapter  	tabTitles 8com.ttv.demo.StickyBottomTabDemoActivity.TabPagerAdapter  	animation com.ttv.demo.android  Animator com.ttv.demo.android.animation  AnimatorListenerAdapter com.ttv.demo.android.animation  apply kotlin  minus kotlin.Float  plus kotlin.Float  invoke kotlin.Function0  
unaryMinus 
kotlin.Int  forEach kotlin.collections  forEach kotlin.sequences  forEach kotlin.text  Math android.app.Activity  Suppress android.app.Activity  Triple android.app.Activity  	ViewGroup android.app.Activity  android android.app.Activity  apply android.app.Activity  LayoutParams android.app.Activity.ViewGroup  Math android.content.Context  Suppress android.content.Context  Triple android.content.Context  	ViewGroup android.content.Context  android android.content.Context  apply android.content.Context  LayoutParams !android.content.Context.ViewGroup  Math android.content.ContextWrapper  Suppress android.content.ContextWrapper  Triple android.content.ContextWrapper  	ViewGroup android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  LayoutParams (android.content.ContextWrapper.ViewGroup  Math  android.view.ContextThemeWrapper  Suppress  android.view.ContextThemeWrapper  Triple  android.view.ContextThemeWrapper  	ViewGroup  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  LayoutParams *android.view.ContextThemeWrapper.ViewGroup  CoordinatorLayout android.view.View  StickyBottomTabBehavior android.view.View  View android.view.View  apply android.view.View  getTag android.view.View  parent android.view.View  setTag android.view.View  translationY android.view.View  LayoutParams android.view.ViewGroup  
childCount android.view.ViewGroup  indexOfChild android.view.ViewGroup  
removeView android.view.ViewGroup  translationY !android.view.ViewPropertyAnimator  getTag android.widget.LinearLayout  parent android.widget.LinearLayout  setTag android.widget.LinearLayout  translationY android.widget.LinearLayout  Math #androidx.activity.ComponentActivity  Suppress #androidx.activity.ComponentActivity  Triple #androidx.activity.ComponentActivity  	ViewGroup #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  LayoutParams -androidx.activity.ComponentActivity.ViewGroup  Math (androidx.appcompat.app.AppCompatActivity  Suppress (androidx.appcompat.app.AppCompatActivity  Triple (androidx.appcompat.app.AppCompatActivity  	ViewGroup (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  LayoutParams 2androidx.appcompat.app.AppCompatActivity.ViewGroup  addView 3androidx.coordinatorlayout.widget.CoordinatorLayout  
removeView 3androidx.coordinatorlayout.widget.CoordinatorLayout  Math <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  MATCH_PARENT @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  StickyBottomTabBehavior @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  WRAP_CONTENT @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  android @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  apply @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  Math #androidx.core.app.ComponentActivity  Suppress #androidx.core.app.ComponentActivity  Triple #androidx.core.app.ComponentActivity  	ViewGroup #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  LayoutParams -androidx.core.app.ComponentActivity.ViewGroup  Math &androidx.fragment.app.FragmentActivity  Suppress &androidx.fragment.app.FragmentActivity  Triple &androidx.fragment.app.FragmentActivity  	ViewGroup &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  LayoutParams 0androidx.fragment.app.FragmentActivity.ViewGroup  BaseOnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  OnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  addOnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  <SAM-CONSTRUCTOR> Kcom.google.android.material.appbar.AppBarLayout.BaseOnOffsetChangedListener  <SAM-CONSTRUCTOR> Gcom.google.android.material.appbar.AppBarLayout.OnOffsetChangedListener  Math com.ttv.demo  Suppress com.ttv.demo  Triple com.ttv.demo  Math com.ttv.demo.MainActivity  Suppress com.ttv.demo.MainActivity  Triple com.ttv.demo.MainActivity  View com.ttv.demo.MainActivity  	ViewGroup com.ttv.demo.MainActivity  android com.ttv.demo.MainActivity  apply com.ttv.demo.MainActivity  
isTabAtBottom com.ttv.demo.MainActivity  moveTabToBottomManually com.ttv.demo.MainActivity  moveTabToOriginalManually com.ttv.demo.MainActivity  setupBehaviorWithTab com.ttv.demo.MainActivity  setupTabBehaviorProxy com.ttv.demo.MainActivity  LayoutParams #com.ttv.demo.MainActivity.ViewGroup  Math $com.ttv.demo.StickyBottomTabBehavior  handleScrollEffects $com.ttv.demo.StickyBottomTabBehavior  Math (com.ttv.demo.StickyBottomTabDemoActivity  Suppress (com.ttv.demo.StickyBottomTabDemoActivity  Triple (com.ttv.demo.StickyBottomTabDemoActivity  	ViewGroup (com.ttv.demo.StickyBottomTabDemoActivity  android (com.ttv.demo.StickyBottomTabDemoActivity  apply (com.ttv.demo.StickyBottomTabDemoActivity  
isTabAtBottom (com.ttv.demo.StickyBottomTabDemoActivity  moveTabToBottomManually (com.ttv.demo.StickyBottomTabDemoActivity  moveTabToOriginalManually (com.ttv.demo.StickyBottomTabDemoActivity  setupTabBehaviorProxy (com.ttv.demo.StickyBottomTabDemoActivity  LayoutParams 2com.ttv.demo.StickyBottomTabDemoActivity.ViewGroup  LayoutParams com.ttv.demo.ViewGroup  abs java.lang.Math  max java.lang.Math  Suppress kotlin  Triple kotlin  div kotlin.Float  times kotlin.Float  toInt kotlin.Float  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  CoIndicator android.app.Activity  forEachIndexed android.app.Activity  CoIndicator android.content.Context  forEachIndexed android.content.Context  CoIndicator android.content.ContextWrapper  forEachIndexed android.content.ContextWrapper  getDimensionPixelSize android.content.res.Resources  CoIndicator  android.view.ContextThemeWrapper  forEachIndexed  android.view.ContextThemeWrapper  CENTER_VERTICAL android.view.Gravity  	elevation android.view.View  	resources android.view.View  setBackgroundColor android.view.View  
setPadding android.view.View  
HORIZONTAL android.widget.LinearLayout  LayoutParams android.widget.LinearLayout  LinearLayout android.widget.LinearLayout  android android.widget.LinearLayout  apply android.widget.LinearLayout  	elevation android.widget.LinearLayout  gravity android.widget.LinearLayout  orientation android.widget.LinearLayout  setBackgroundColor android.widget.LinearLayout  CoIndicator #androidx.activity.ComponentActivity  forEachIndexed #androidx.activity.ComponentActivity  CoIndicator (androidx.appcompat.app.AppCompatActivity  forEachIndexed (androidx.appcompat.app.AppCompatActivity  forEachIndexed #androidx.core.app.ComponentActivity  CoIndicator &androidx.fragment.app.FragmentActivity  forEachIndexed &androidx.fragment.app.FragmentActivity  currentItem %androidx.viewpager2.widget.ViewPager2  getTabAt *com.google.android.material.tabs.TabLayout  select .com.google.android.material.tabs.TabLayout.Tab  LinearLayout 1com.hbg.module.libkt.custom.indicator.CoIndicator  R 1com.hbg.module.libkt.custom.indicator.CoIndicator  apply 1com.hbg.module.libkt.custom.indicator.CoIndicator  getTabAt 1com.hbg.module.libkt.custom.indicator.CoIndicator  layoutParams 1com.hbg.module.libkt.custom.indicator.CoIndicator  	resources 1com.hbg.module.libkt.custom.indicator.CoIndicator  
setPadding 1com.hbg.module.libkt.custom.indicator.CoIndicator  forEachIndexed com.ttv.demo  createStickyBottomTab com.ttv.demo.MainActivity  forEachIndexed com.ttv.demo.MainActivity  removeStickyBottomTab com.ttv.demo.MainActivity  setupTabSync com.ttv.demo.MainActivity  stickyBottomTabContainer com.ttv.demo.MainActivity  dimen_40 com.ttv.demo.R.dimen  dimen_8 com.ttv.demo.R.dimen  createStickyBottomTab (com.ttv.demo.StickyBottomTabDemoActivity  forEachIndexed (com.ttv.demo.StickyBottomTabDemoActivity  let (com.ttv.demo.StickyBottomTabDemoActivity  removeStickyBottomTab (com.ttv.demo.StickyBottomTabDemoActivity  setupTabSync (com.ttv.demo.StickyBottomTabDemoActivity  stickyBottomTabContainer (com.ttv.demo.StickyBottomTabDemoActivity  toInt kotlin.Long  forEachIndexed kotlin.collections  forEachIndexed kotlin.collections.List  forEachIndexed kotlin.sequences  forEachIndexed kotlin.text  Boolean #androidx.core.app.ComponentActivity  Boolean com.ttv.demo.MainActivity  IntArray com.ttv.demo.MainActivity  isOriginalTabVisible com.ttv.demo.MainActivity  Boolean (com.ttv.demo.StickyBottomTabDemoActivity  IntArray (com.ttv.demo.StickyBottomTabDemoActivity  isOriginalTabVisible (com.ttv.demo.StickyBottomTabDemoActivity  plus 
kotlin.Int  checkAndUpdateTabVisibility com.ttv.demo.MainActivity  checkAndUpdateTabVisibility (com.ttv.demo.StickyBottomTabDemoActivity  checkAndUpdateTabVisibility android.app.Activity  checkAndUpdateTabVisibility android.content.Context  checkAndUpdateTabVisibility android.content.ContextWrapper  d android.util.Log  checkAndUpdateTabVisibility  android.view.ContextThemeWrapper  checkAndUpdateTabVisibility #androidx.activity.ComponentActivity  checkAndUpdateTabVisibility (androidx.appcompat.app.AppCompatActivity  checkAndUpdateTabVisibility #androidx.core.app.ComponentActivity  checkAndUpdateTabVisibility &androidx.fragment.app.FragmentActivity  checkAndUpdateTabVisibility com.ttv.demo  ViewTreeObserver com.ttv.demo.MainActivity  setupInitialTabCheck com.ttv.demo.MainActivity  OnGlobalLayoutListener *com.ttv.demo.MainActivity.ViewTreeObserver  ViewTreeObserver (com.ttv.demo.StickyBottomTabDemoActivity  setupInitialTabCheck (com.ttv.demo.StickyBottomTabDemoActivity  OnGlobalLayoutListener 9com.ttv.demo.StickyBottomTabDemoActivity.ViewTreeObserver  'checkAndUpdateTabVisibilityDuringScroll com.ttv.demo.MainActivity  checkForTabConflict com.ttv.demo.MainActivity  showStickyBottomTabByDefault com.ttv.demo.MainActivity  'checkAndUpdateTabVisibilityDuringScroll (com.ttv.demo.StickyBottomTabDemoActivity  checkForTabConflict (com.ttv.demo.StickyBottomTabDemoActivity  showStickyBottomTabByDefault (com.ttv.demo.StickyBottomTabDemoActivity  
isTabAtBottom android.app.Activity  
isTabAtBottom android.content.Context  
isTabAtBottom android.content.ContextWrapper  
isTabAtBottom  android.view.ContextThemeWrapper  
isTabAtBottom #androidx.activity.ComponentActivity  
isTabAtBottom (androidx.appcompat.app.AppCompatActivity  
isTabAtBottom #androidx.core.app.ComponentActivity  
isTabAtBottom &androidx.fragment.app.FragmentActivity  
isTabAtBottom :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  
isTabAtBottom com.ttv.demo  OnPageChangeCallback $com.ttv.demo.MainActivity.ViewPager2  OnPageChangeCallback 3com.ttv.demo.StickyBottomTabDemoActivity.ViewPager2  
coerceAtLeast android.app.Activity  
coerceAtLeast android.content.Context  
coerceAtLeast android.content.ContextWrapper  
coerceAtLeast  android.view.ContextThemeWrapper  scrollY android.view.View  setInterpolator !android.view.ViewPropertyAnimator  DecelerateInterpolator android.view.animation  
coerceAtLeast #androidx.activity.ComponentActivity  
coerceAtLeast (androidx.appcompat.app.AppCompatActivity  
coerceAtLeast #androidx.core.app.ComponentActivity  scrollY %androidx.core.widget.NestedScrollView  
coerceAtLeast &androidx.fragment.app.FragmentActivity  setCurrentItem %androidx.viewpager2.widget.ViewPager2  
coerceAtLeast com.ttv.demo  
coerceAtLeast com.ttv.demo.MainActivity  performSmoothTabSwitch com.ttv.demo.MainActivity   scrollToOneThirdPositionSmoothly com.ttv.demo.MainActivity  
coerceAtLeast (com.ttv.demo.StickyBottomTabDemoActivity  performSmoothTabSwitch (com.ttv.demo.StickyBottomTabDemoActivity   scrollToOneThirdPositionSmoothly (com.ttv.demo.StickyBottomTabDemoActivity  
coerceAtLeast 
kotlin.Int  
coerceAtLeast 
kotlin.ranges  coerceIn android.app.Activity  coerceIn android.content.Context  coerceIn android.content.ContextWrapper  coerceIn  android.view.ContextThemeWrapper  coerceIn #androidx.activity.ComponentActivity  coerceIn (androidx.appcompat.app.AppCompatActivity  coerceIn #androidx.core.app.ComponentActivity  coerceIn &androidx.fragment.app.FragmentActivity  getLocationOnScreen /com.google.android.material.appbar.AppBarLayout  coerceIn com.ttv.demo  coerceIn com.ttv.demo.MainActivity  coerceIn (com.ttv.demo.StickyBottomTabDemoActivity  coerceIn 
kotlin.Int  coerceIn 
kotlin.ranges  coerceAtMost android.app.Activity  coerceAtMost android.content.Context  coerceAtMost android.content.ContextWrapper  coerceAtMost  android.view.ContextThemeWrapper  top android.view.View  
getChildAt android.view.ViewGroup  top android.widget.LinearLayout  coerceAtMost #androidx.activity.ComponentActivity  coerceAtMost (androidx.appcompat.app.AppCompatActivity  coerceAtMost #androidx.core.app.ComponentActivity  
getChildAt %androidx.core.widget.NestedScrollView  height %androidx.core.widget.NestedScrollView  coerceAtMost &androidx.fragment.app.FragmentActivity  top /com.google.android.material.appbar.AppBarLayout  coerceAtMost com.ttv.demo  coerceAtMost com.ttv.demo.MainActivity  coerceAtMost (com.ttv.demo.StickyBottomTabDemoActivity  coerceAtMost 
kotlin.Int  plus 
kotlin.String  coerceAtMost 
kotlin.ranges  
setMargins )android.view.ViewGroup.MarginLayoutParams  MATCH_PARENT (android.widget.LinearLayout.LayoutParams  apply (android.widget.LinearLayout.LayoutParams  
setMargins (android.widget.LinearLayout.LayoutParams  LinearLayout android.widget.TextView  apply android.widget.TextView  layoutParams android.widget.TextView  setBackgroundColor android.widget.TextView  
setPadding android.widget.TextView  textSize android.widget.TextView  min java.lang.Math  rem 
kotlin.Int  interpolator android.animation.ValueAnimator  ofInt android.animation.ValueAnimator  
requestLayout android.view.ViewGroup  
requestLayout /com.google.android.material.appbar.AppBarLayout  !scrollToShowOriginalTabAtOneThird (com.ttv.demo.StickyBottomTabDemoActivity  createStickyBottomTab android.app.Activity  
isInitialized android.app.Activity  isOriginalTabVisible android.app.Activity  view android.app.Activity.android  ViewTreeObserver !android.app.Activity.android.view  OnGlobalLayoutListener 2android.app.Activity.android.view.ViewTreeObserver  createStickyBottomTab android.content.Context  
isInitialized android.content.Context  isOriginalTabVisible android.content.Context  view android.content.Context.android  ViewTreeObserver $android.content.Context.android.view  OnGlobalLayoutListener 5android.content.Context.android.view.ViewTreeObserver  createStickyBottomTab android.content.ContextWrapper  
isInitialized android.content.ContextWrapper  isOriginalTabVisible android.content.ContextWrapper  view &android.content.ContextWrapper.android  ViewTreeObserver +android.content.ContextWrapper.android.view  OnGlobalLayoutListener <android.content.ContextWrapper.android.view.ViewTreeObserver  w android.util.Log  createStickyBottomTab  android.view.ContextThemeWrapper  
isInitialized  android.view.ContextThemeWrapper  isOriginalTabVisible  android.view.ContextThemeWrapper  view (android.view.ContextThemeWrapper.android  ViewTreeObserver -android.view.ContextThemeWrapper.android.view  OnGlobalLayoutListener >android.view.ContextThemeWrapper.android.view.ViewTreeObserver  	isLaidOut android.view.View  	isLaidOut android.widget.LinearLayout  createStickyBottomTab #androidx.activity.ComponentActivity  
isInitialized #androidx.activity.ComponentActivity  isOriginalTabVisible #androidx.activity.ComponentActivity  view +androidx.activity.ComponentActivity.android  ViewTreeObserver 0androidx.activity.ComponentActivity.android.view  OnGlobalLayoutListener Aandroidx.activity.ComponentActivity.android.view.ViewTreeObserver  createStickyBottomTab (androidx.appcompat.app.AppCompatActivity  
isInitialized (androidx.appcompat.app.AppCompatActivity  isOriginalTabVisible (androidx.appcompat.app.AppCompatActivity  view 0androidx.appcompat.app.AppCompatActivity.android  ViewTreeObserver 5androidx.appcompat.app.AppCompatActivity.android.view  OnGlobalLayoutListener Fandroidx.appcompat.app.AppCompatActivity.android.view.ViewTreeObserver  createStickyBottomTab #androidx.core.app.ComponentActivity  
isInitialized #androidx.core.app.ComponentActivity  isOriginalTabVisible #androidx.core.app.ComponentActivity  view +androidx.core.app.ComponentActivity.android  ViewTreeObserver 0androidx.core.app.ComponentActivity.android.view  OnGlobalLayoutListener Aandroidx.core.app.ComponentActivity.android.view.ViewTreeObserver  createStickyBottomTab &androidx.fragment.app.FragmentActivity  
isInitialized &androidx.fragment.app.FragmentActivity  isOriginalTabVisible &androidx.fragment.app.FragmentActivity  view .androidx.fragment.app.FragmentActivity.android  ViewTreeObserver 3androidx.fragment.app.FragmentActivity.android.view  OnGlobalLayoutListener Dandroidx.fragment.app.FragmentActivity.android.view.ViewTreeObserver  createStickyBottomTab com.ttv.demo  
isInitialized com.ttv.demo  isOriginalTabVisible com.ttv.demo  
isInitialized com.ttv.demo.MainActivity  view !com.ttv.demo.MainActivity.android  ViewTreeObserver &com.ttv.demo.MainActivity.android.view  OnGlobalLayoutListener 7com.ttv.demo.MainActivity.android.view.ViewTreeObserver  
isInitialized (com.ttv.demo.StickyBottomTabDemoActivity  view 0com.ttv.demo.StickyBottomTabDemoActivity.android  ViewTreeObserver 5com.ttv.demo.StickyBottomTabDemoActivity.android.view  OnGlobalLayoutListener Fcom.ttv.demo.StickyBottomTabDemoActivity.android.view.ViewTreeObserver  view com.ttv.demo.android  ViewTreeObserver com.ttv.demo.android.view  OnGlobalLayoutListener *com.ttv.demo.android.view.ViewTreeObserver  
isInitialized kotlin  KMutableProperty0 kotlin.reflect  
isInitialized  kotlin.reflect.KMutableProperty0  $checkOriginalTabVisibilityForStartup android.app.Activity  $checkOriginalTabVisibilityForStartup android.content.Context  $checkOriginalTabVisibilityForStartup android.content.ContextWrapper  $checkOriginalTabVisibilityForStartup  android.view.ContextThemeWrapper  $checkOriginalTabVisibilityForStartup #androidx.activity.ComponentActivity  $checkOriginalTabVisibilityForStartup (androidx.appcompat.app.AppCompatActivity  $checkOriginalTabVisibilityForStartup #androidx.core.app.ComponentActivity  $checkOriginalTabVisibilityForStartup &androidx.fragment.app.FragmentActivity  $checkOriginalTabVisibilityForStartup com.ttv.demo  $checkOriginalTabVisibilityForStartup com.ttv.demo.MainActivity  $checkOriginalTabVisibilityForStartup (com.ttv.demo.StickyBottomTabDemoActivity  isInitializing android.app.Activity  isInitializing android.content.Context  isInitializing android.content.ContextWrapper  isInitializing  android.view.ContextThemeWrapper  isInitializing #androidx.activity.ComponentActivity  isInitializing (androidx.appcompat.app.AppCompatActivity  isInitializing #androidx.core.app.ComponentActivity  isInitializing &androidx.fragment.app.FragmentActivity  isInitializing :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  isInitializing com.ttv.demo  isInitializing com.ttv.demo.MainActivity  isInitializing (com.ttv.demo.StickyBottomTabDemoActivity  isInitializing com.ttv.demo.StickyTabActivity  checkAndShowStickyTabIfNeeded android.app.Activity  checkAndShowStickyTabIfNeeded android.content.Context  checkAndShowStickyTabIfNeeded android.content.ContextWrapper  checkAndShowStickyTabIfNeeded  android.view.ContextThemeWrapper  checkAndShowStickyTabIfNeeded #androidx.activity.ComponentActivity  checkAndShowStickyTabIfNeeded (androidx.appcompat.app.AppCompatActivity  checkAndShowStickyTabIfNeeded #androidx.core.app.ComponentActivity  checkAndShowStickyTabIfNeeded &androidx.fragment.app.FragmentActivity  checkAndShowStickyTabIfNeeded com.ttv.demo  checkAndShowStickyTabIfNeeded com.ttv.demo.MainActivity  checkAndShowStickyTabIfNeeded (com.ttv.demo.StickyBottomTabDemoActivity  getLocationOnScreen 3androidx.coordinatorlayout.widget.CoordinatorLayout  shouldShowStickyBottomTab (com.ttv.demo.StickyBottomTabDemoActivity  android )android.animation.AnimatorListenerAdapter  coordinatorLayout )android.animation.AnimatorListenerAdapter  
isTabAtBottom )android.animation.AnimatorListenerAdapter  let )android.animation.AnimatorListenerAdapter  stickyBottomTabContainer )android.animation.AnimatorListenerAdapter  !scrollToShowOriginalTabAtOneThird android.app.Activity  stickyBottomTabContainer android.app.Activity  	viewPager android.app.Activity  	animation android.app.Activity.android  Animator &android.app.Activity.android.animation  AnimatorListenerAdapter &android.app.Activity.android.animation  !scrollToShowOriginalTabAtOneThird android.content.Context  stickyBottomTabContainer android.content.Context  	viewPager android.content.Context  	animation android.content.Context.android  Animator )android.content.Context.android.animation  AnimatorListenerAdapter )android.content.Context.android.animation  !scrollToShowOriginalTabAtOneThird android.content.ContextWrapper  stickyBottomTabContainer android.content.ContextWrapper  	viewPager android.content.ContextWrapper  	animation &android.content.ContextWrapper.android  Animator 0android.content.ContextWrapper.android.animation  AnimatorListenerAdapter 0android.content.ContextWrapper.android.animation  !scrollToShowOriginalTabAtOneThird  android.view.ContextThemeWrapper  stickyBottomTabContainer  android.view.ContextThemeWrapper  	viewPager  android.view.ContextThemeWrapper  	animation (android.view.ContextThemeWrapper.android  Animator 2android.view.ContextThemeWrapper.android.animation  AnimatorListenerAdapter 2android.view.ContextThemeWrapper.android.animation  setListener !android.view.ViewPropertyAnimator  !scrollToShowOriginalTabAtOneThird #androidx.activity.ComponentActivity  stickyBottomTabContainer #androidx.activity.ComponentActivity  	viewPager #androidx.activity.ComponentActivity  	animation +androidx.activity.ComponentActivity.android  Animator 5androidx.activity.ComponentActivity.android.animation  AnimatorListenerAdapter 5androidx.activity.ComponentActivity.android.animation  !scrollToShowOriginalTabAtOneThird (androidx.appcompat.app.AppCompatActivity  stickyBottomTabContainer (androidx.appcompat.app.AppCompatActivity  	viewPager (androidx.appcompat.app.AppCompatActivity  	animation 0androidx.appcompat.app.AppCompatActivity.android  Animator :androidx.appcompat.app.AppCompatActivity.android.animation  AnimatorListenerAdapter :androidx.appcompat.app.AppCompatActivity.android.animation  !scrollToShowOriginalTabAtOneThird #androidx.core.app.ComponentActivity  stickyBottomTabContainer #androidx.core.app.ComponentActivity  	viewPager #androidx.core.app.ComponentActivity  	animation +androidx.core.app.ComponentActivity.android  Animator 5androidx.core.app.ComponentActivity.android.animation  AnimatorListenerAdapter 5androidx.core.app.ComponentActivity.android.animation  !scrollToShowOriginalTabAtOneThird &androidx.fragment.app.FragmentActivity  stickyBottomTabContainer &androidx.fragment.app.FragmentActivity  	viewPager &androidx.fragment.app.FragmentActivity  	animation .androidx.fragment.app.FragmentActivity.android  Animator 8androidx.fragment.app.FragmentActivity.android.animation  AnimatorListenerAdapter 8androidx.fragment.app.FragmentActivity.android.animation  SCROLL_STATE_IDLE %androidx.viewpager2.widget.ViewPager2  unregisterOnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  
ViewPager2 :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  android :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  !scrollToShowOriginalTabAtOneThird :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  	viewPager :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  !scrollToShowOriginalTabAtOneThird com.ttv.demo  stickyBottomTabContainer com.ttv.demo  	viewPager com.ttv.demo   switchToPageAndWaitForCompletion (com.ttv.demo.StickyBottomTabDemoActivity  	animation 0com.ttv.demo.StickyBottomTabDemoActivity.android  Animator :com.ttv.demo.StickyBottomTabDemoActivity.android.animation  AnimatorListenerAdapter :com.ttv.demo.StickyBottomTabDemoActivity.android.animation  	Exception android.app.Activity  	Exception android.content.Context  	Exception android.content.ContextWrapper  e android.util.Log  	Exception  android.view.ContextThemeWrapper  	Exception #androidx.activity.ComponentActivity  	Exception (androidx.appcompat.app.AppCompatActivity  	Exception #androidx.core.app.ComponentActivity  	Exception &androidx.fragment.app.FragmentActivity  	Exception com.ttv.demo  	Exception com.ttv.demo.MainActivity  	Exception (com.ttv.demo.StickyBottomTabDemoActivity  	Exception 	java.lang  appBarLayout android.app.Activity  appBarLayout android.content.Context  appBarLayout android.content.ContextWrapper  appBarLayout  android.view.ContextThemeWrapper  appBarLayout #androidx.activity.ComponentActivity  appBarLayout (androidx.appcompat.app.AppCompatActivity  appBarLayout #androidx.core.app.ComponentActivity  appBarLayout &androidx.fragment.app.FragmentActivity  appBarLayout com.ttv.demo  	Exception )android.animation.AnimatorListenerAdapter  isAnimating )android.animation.AnimatorListenerAdapter  isAnimating android.app.Activity  isAnimating android.content.Context  isAnimating android.content.ContextWrapper  isAnimating  android.view.ContextThemeWrapper  isAnimating #androidx.activity.ComponentActivity  isAnimating (androidx.appcompat.app.AppCompatActivity  isAnimating #androidx.core.app.ComponentActivity  isAnimating &androidx.fragment.app.FragmentActivity  isAnimating com.ttv.demo  isAnimating (com.ttv.demo.StickyBottomTabDemoActivity  isAnimating com.ttv.demo.MainActivity  Int )android.animation.AnimatorListenerAdapter  androidx )android.animation.AnimatorListenerAdapter  nestedScrollView )android.animation.AnimatorListenerAdapter  	viewPager )android.animation.AnimatorListenerAdapter  
viewpager2 2android.animation.AnimatorListenerAdapter.androidx  widget =android.animation.AnimatorListenerAdapter.androidx.viewpager2  
ViewPager2 Dandroid.animation.AnimatorListenerAdapter.androidx.viewpager2.widget  OnPageChangeCallback Oandroid.animation.AnimatorListenerAdapter.androidx.viewpager2.widget.ViewPager2  androidx android.app.Activity  nestedScrollView android.app.Activity  
viewpager2 android.app.Activity.androidx  widget (android.app.Activity.androidx.viewpager2  
ViewPager2 /android.app.Activity.androidx.viewpager2.widget  OnPageChangeCallback :android.app.Activity.androidx.viewpager2.widget.ViewPager2  androidx android.content.Context  nestedScrollView android.content.Context  
viewpager2  android.content.Context.androidx  widget +android.content.Context.androidx.viewpager2  
ViewPager2 2android.content.Context.androidx.viewpager2.widget  OnPageChangeCallback =android.content.Context.androidx.viewpager2.widget.ViewPager2  androidx android.content.ContextWrapper  nestedScrollView android.content.ContextWrapper  
viewpager2 'android.content.ContextWrapper.androidx  widget 2android.content.ContextWrapper.androidx.viewpager2  
ViewPager2 9android.content.ContextWrapper.androidx.viewpager2.widget  OnPageChangeCallback Dandroid.content.ContextWrapper.androidx.viewpager2.widget.ViewPager2  androidx  android.view.ContextThemeWrapper  nestedScrollView  android.view.ContextThemeWrapper  
viewpager2 )android.view.ContextThemeWrapper.androidx  widget 4android.view.ContextThemeWrapper.androidx.viewpager2  
ViewPager2 ;android.view.ContextThemeWrapper.androidx.viewpager2.widget  OnPageChangeCallback Fandroid.view.ContextThemeWrapper.androidx.viewpager2.widget.ViewPager2  TOP android.view.Gravity  androidx #androidx.activity.ComponentActivity  nestedScrollView #androidx.activity.ComponentActivity  
viewpager2 ,androidx.activity.ComponentActivity.androidx  widget 7androidx.activity.ComponentActivity.androidx.viewpager2  
ViewPager2 >androidx.activity.ComponentActivity.androidx.viewpager2.widget  OnPageChangeCallback Iandroidx.activity.ComponentActivity.androidx.viewpager2.widget.ViewPager2  androidx (androidx.appcompat.app.AppCompatActivity  nestedScrollView (androidx.appcompat.app.AppCompatActivity  
viewpager2 1androidx.appcompat.app.AppCompatActivity.androidx  widget <androidx.appcompat.app.AppCompatActivity.androidx.viewpager2  
ViewPager2 Candroidx.appcompat.app.AppCompatActivity.androidx.viewpager2.widget  OnPageChangeCallback Nandroidx.appcompat.app.AppCompatActivity.androidx.viewpager2.widget.ViewPager2  androidx #androidx.core.app.ComponentActivity  nestedScrollView #androidx.core.app.ComponentActivity  
viewpager2 ,androidx.core.app.ComponentActivity.androidx  widget 7androidx.core.app.ComponentActivity.androidx.viewpager2  
ViewPager2 >androidx.core.app.ComponentActivity.androidx.viewpager2.widget  OnPageChangeCallback Iandroidx.core.app.ComponentActivity.androidx.viewpager2.widget.ViewPager2  androidx &androidx.fragment.app.FragmentActivity  nestedScrollView &androidx.fragment.app.FragmentActivity  
viewpager2 /androidx.fragment.app.FragmentActivity.androidx  widget :androidx.fragment.app.FragmentActivity.androidx.viewpager2  
ViewPager2 Aandroidx.fragment.app.FragmentActivity.androidx.viewpager2.widget  OnPageChangeCallback Landroidx.fragment.app.FragmentActivity.androidx.viewpager2.widget.ViewPager2  androidx :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  nestedScrollView :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  androidx com.ttv.demo  nestedScrollView com.ttv.demo  androidx (com.ttv.demo.StickyBottomTabDemoActivity  createStickyTopTab (com.ttv.demo.StickyBottomTabDemoActivity  
isTabAtTop (com.ttv.demo.StickyBottomTabDemoActivity  performSmoothTabSwitchFromTop (com.ttv.demo.StickyBottomTabDemoActivity  removeStickyTopTab (com.ttv.demo.StickyBottomTabDemoActivity  stickyTopTabContainer (com.ttv.demo.StickyBottomTabDemoActivity  
viewpager2 1com.ttv.demo.StickyBottomTabDemoActivity.androidx  widget <com.ttv.demo.StickyBottomTabDemoActivity.androidx.viewpager2  
ViewPager2 Ccom.ttv.demo.StickyBottomTabDemoActivity.androidx.viewpager2.widget  OnPageChangeCallback Ncom.ttv.demo.StickyBottomTabDemoActivity.androidx.viewpager2.widget.ViewPager2  
viewpager2 com.ttv.demo.androidx  widget  com.ttv.demo.androidx.viewpager2  
ViewPager2 'com.ttv.demo.androidx.viewpager2.widget  OnPageChangeCallback 2com.ttv.demo.androidx.viewpager2.widget.ViewPager2  
unaryMinus kotlin.Float  ScrollDirection android.app.Activity  isBottomTabVisible android.app.Activity  isTopTabVisible android.app.Activity  ScrollDirection android.content.Context  isBottomTabVisible android.content.Context  isTopTabVisible android.content.Context  ScrollDirection android.content.ContextWrapper  isBottomTabVisible android.content.ContextWrapper  isTopTabVisible android.content.ContextWrapper  ScrollDirection  android.view.ContextThemeWrapper  isBottomTabVisible  android.view.ContextThemeWrapper  isTopTabVisible  android.view.ContextThemeWrapper  ScrollDirection #androidx.activity.ComponentActivity  isBottomTabVisible #androidx.activity.ComponentActivity  isTopTabVisible #androidx.activity.ComponentActivity  ScrollDirection (androidx.appcompat.app.AppCompatActivity  isBottomTabVisible (androidx.appcompat.app.AppCompatActivity  isTopTabVisible (androidx.appcompat.app.AppCompatActivity  ScrollDirection #androidx.core.app.ComponentActivity  Unit #androidx.core.app.ComponentActivity  isBottomTabVisible #androidx.core.app.ComponentActivity  isTopTabVisible #androidx.core.app.ComponentActivity  ScrollDirection &androidx.fragment.app.FragmentActivity  isBottomTabVisible &androidx.fragment.app.FragmentActivity  isTopTabVisible &androidx.fragment.app.FragmentActivity  isBottomTabVisible :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  isTopTabVisible :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  ScrollDirection com.ttv.demo  isBottomTabVisible com.ttv.demo  isTopTabVisible com.ttv.demo  ScrollDirection (com.ttv.demo.StickyBottomTabDemoActivity  Unit (com.ttv.demo.StickyBottomTabDemoActivity  !checkAndUpdateStickyTabVisibility (com.ttv.demo.StickyBottomTabDemoActivity  createStickyIndicator (com.ttv.demo.StickyBottomTabDemoActivity  createStickyTabContainer (com.ttv.demo.StickyBottomTabDemoActivity  hideBottomStickyTab (com.ttv.demo.StickyBottomTabDemoActivity  hideTopStickyTab (com.ttv.demo.StickyBottomTabDemoActivity  isBottomTabVisible (com.ttv.demo.StickyBottomTabDemoActivity  isTopTabVisible (com.ttv.demo.StickyBottomTabDemoActivity  lastScrollY (com.ttv.demo.StickyBottomTabDemoActivity   performSmoothTabSwitchFromBottom (com.ttv.demo.StickyBottomTabDemoActivity  scrollDirection (com.ttv.demo.StickyBottomTabDemoActivity  scrollToShowOriginalTab (com.ttv.demo.StickyBottomTabDemoActivity  setupStickyTabBehavior (com.ttv.demo.StickyBottomTabDemoActivity  showBottomStickyTab (com.ttv.demo.StickyBottomTabDemoActivity  showTopStickyTab (com.ttv.demo.StickyBottomTabDemoActivity  DOWN 8com.ttv.demo.StickyBottomTabDemoActivity.ScrollDirection  NONE 8com.ttv.demo.StickyBottomTabDemoActivity.ScrollDirection  UP 8com.ttv.demo.StickyBottomTabDemoActivity.ScrollDirection  id android.view.View  R <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  until <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  LayoutParams Iandroidx.coordinatorlayout.widget.CoordinatorLayout.Behavior.AppBarLayout  LayoutParams /com.google.android.material.appbar.AppBarLayout  
childCount /com.google.android.material.appbar.AppBarLayout  
getChildAt /com.google.android.material.appbar.AppBarLayout  AppBarLayout <com.google.android.material.appbar.AppBarLayout.BaseBehavior  R <com.google.android.material.appbar.AppBarLayout.BaseBehavior  android <com.google.android.material.appbar.AppBarLayout.BaseBehavior  until <com.google.android.material.appbar.AppBarLayout.BaseBehavior  LayoutParams Icom.google.android.material.appbar.AppBarLayout.BaseBehavior.AppBarLayout  AppBarLayout 8com.google.android.material.appbar.AppBarLayout.Behavior  R 8com.google.android.material.appbar.AppBarLayout.Behavior  android 8com.google.android.material.appbar.AppBarLayout.Behavior  
onLayoutChild 8com.google.android.material.appbar.AppBarLayout.Behavior  until 8com.google.android.material.appbar.AppBarLayout.Behavior  LayoutParams Ecom.google.android.material.appbar.AppBarLayout.Behavior.AppBarLayout  SCROLL_FLAG_ENTER_ALWAYS <com.google.android.material.appbar.AppBarLayout.LayoutParams  SCROLL_FLAG_SCROLL <com.google.android.material.appbar.AppBarLayout.LayoutParams  SCROLL_FLAG_SNAP <com.google.android.material.appbar.AppBarLayout.LayoutParams  scrollFlags <com.google.android.material.appbar.AppBarLayout.LayoutParams  AppBarLayout 1com.google.android.material.appbar.HeaderBehavior  R 1com.google.android.material.appbar.HeaderBehavior  android 1com.google.android.material.appbar.HeaderBehavior  until 1com.google.android.material.appbar.HeaderBehavior  LayoutParams >com.google.android.material.appbar.HeaderBehavior.AppBarLayout  AppBarLayout 5com.google.android.material.appbar.ViewOffsetBehavior  R 5com.google.android.material.appbar.ViewOffsetBehavior  android 5com.google.android.material.appbar.ViewOffsetBehavior  until 5com.google.android.material.appbar.ViewOffsetBehavior  LayoutParams Bcom.google.android.material.appbar.ViewOffsetBehavior.AppBarLayout  Boolean !com.hbg.module.libkt.custom.coord  R !com.hbg.module.libkt.custom.coord  android !com.hbg.module.libkt.custom.coord  until !com.hbg.module.libkt.custom.coord  LayoutParams .com.hbg.module.libkt.custom.coord.AppBarLayout  AppBarLayout 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  R 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  android 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  handleStickyViewsOnScroll 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  isStickyView 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  processStickyViews 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  setupStickyBehavior 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  until 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  checkBottomStickyTabVisibility (com.ttv.demo.StickyBottomTabDemoActivity  setupBottomTabSync (com.ttv.demo.StickyBottomTabDemoActivity  or 
kotlin.Int  	CharRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  until 
kotlin.ranges  CENTER android.view.Gravity  R android.widget.TextView  android android.widget.TextView  gravity android.widget.TextView  	resources android.widget.TextView  setTextColor android.widget.TextView  	dimen_200 com.ttv.demo.R.dimen  abs android.app.Activity  abs android.content.Context  abs android.content.ContextWrapper  abs  android.view.ContextThemeWrapper  abs #androidx.activity.ComponentActivity  abs (androidx.appcompat.app.AppCompatActivity  coerceIn <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  max <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  abs #androidx.core.app.ComponentActivity  abs &androidx.fragment.app.FragmentActivity  coerceIn <com.google.android.material.appbar.AppBarLayout.BaseBehavior  max <com.google.android.material.appbar.AppBarLayout.BaseBehavior  coerceIn 8com.google.android.material.appbar.AppBarLayout.Behavior  max 8com.google.android.material.appbar.AppBarLayout.Behavior  coerceIn 1com.google.android.material.appbar.HeaderBehavior  max 1com.google.android.material.appbar.HeaderBehavior  coerceIn 5com.google.android.material.appbar.ViewOffsetBehavior  max 5com.google.android.material.appbar.ViewOffsetBehavior  coerceIn !com.hbg.module.libkt.custom.coord  max !com.hbg.module.libkt.custom.coord  calculateStickyOffset 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  coerceIn 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  contentView 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  	findViews 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  max 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  tabView 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  topAndBottomOffset 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  abs com.ttv.demo  abs (com.ttv.demo.StickyBottomTabDemoActivity  set kotlin.IntArray  max kotlin.collections  abs kotlin.math  max kotlin.math  min kotlin.math  max kotlin.sequences  max kotlin.text  
app_icon_size android.R.dimen  notification_large_icon_width android.R.dimen  config )android.animation.AnimatorListenerAdapter  StickyBottomTabPluginFactory android.app.Activity  createForCoIndicator android.app.Activity  StickyBottomTabPluginFactory android.content.Context  createForCoIndicator android.content.Context  	resources android.content.Context  StickyBottomTabPluginFactory android.content.ContextWrapper  createForCoIndicator android.content.ContextWrapper  Drawable android.graphics.drawable  Gravity android.view  StickyBottomTabPluginFactory  android.view.ContextThemeWrapper  createForCoIndicator  android.view.ContextThemeWrapper  context android.view.View  getLocationOnScreen android.view.ViewGroup  height android.view.ViewGroup  Gravity android.widget.LinearLayout  config android.widget.LinearLayout  WRAP_CONTENT (android.widget.LinearLayout.LayoutParams  StickyBottomTabPluginFactory #androidx.activity.ComponentActivity  createForCoIndicator #androidx.activity.ComponentActivity  StickyBottomTabPluginFactory (androidx.appcompat.app.AppCompatActivity  createForCoIndicator (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  Gravity @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  StickyBottomTabPlugin #androidx.core.app.ComponentActivity  StickyBottomTabPluginFactory #androidx.core.app.ComponentActivity  createForCoIndicator #androidx.core.app.ComponentActivity  StickyBottomTabPluginFactory &androidx.fragment.app.FragmentActivity  createForCoIndicator &androidx.fragment.app.FragmentActivity  let %androidx.viewpager2.widget.ViewPager2  	TabLayout :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  let :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  removeOnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  let Gcom.google.android.material.appbar.AppBarLayout.OnOffsetChangedListener  LinearLayout *com.google.android.material.tabs.TabLayout  apply *com.google.android.material.tabs.TabLayout  layoutParams *com.google.android.material.tabs.TabLayout  selectedTabPosition *com.google.android.material.tabs.TabLayout  tabCount *com.google.android.material.tabs.TabLayout  icon .com.google.android.material.tabs.TabLayout.Tab  android 1com.hbg.module.libkt.custom.indicator.CoIndicator  context 1com.hbg.module.libkt.custom.indicator.CoIndicator  tabCount 1com.hbg.module.libkt.custom.indicator.CoIndicator  Animator "com.hbg.module.libkt.custom.sticky  AnimatorListenerAdapter "com.hbg.module.libkt.custom.sticky  AppBarLayout "com.hbg.module.libkt.custom.sticky  Boolean "com.hbg.module.libkt.custom.sticky  CoIndicator "com.hbg.module.libkt.custom.sticky  CoIndicatorAware "com.hbg.module.libkt.custom.sticky   CoIndicatorStickyBottomTabPlugin "com.hbg.module.libkt.custom.sticky  Config "com.hbg.module.libkt.custom.sticky  Context "com.hbg.module.libkt.custom.sticky  CoordinatorLayout "com.hbg.module.libkt.custom.sticky  	Exception "com.hbg.module.libkt.custom.sticky  Float "com.hbg.module.libkt.custom.sticky  Gravity "com.hbg.module.libkt.custom.sticky  Int "com.hbg.module.libkt.custom.sticky  IntArray "com.hbg.module.libkt.custom.sticky  LinearLayout "com.hbg.module.libkt.custom.sticky  List "com.hbg.module.libkt.custom.sticky  Long "com.hbg.module.libkt.custom.sticky  StickyBottomTabPlugin "com.hbg.module.libkt.custom.sticky  StickyBottomTabPluginFactory "com.hbg.module.libkt.custom.sticky  String "com.hbg.module.libkt.custom.sticky  	TabLayout "com.hbg.module.libkt.custom.sticky  Unit "com.hbg.module.libkt.custom.sticky  	ViewGroup "com.hbg.module.libkt.custom.sticky  
ViewPager2 "com.hbg.module.libkt.custom.sticky  android "com.hbg.module.libkt.custom.sticky  androidx "com.hbg.module.libkt.custom.sticky  apply "com.hbg.module.libkt.custom.sticky  com "com.hbg.module.libkt.custom.sticky  config "com.hbg.module.libkt.custom.sticky  	emptyList "com.hbg.module.libkt.custom.sticky  forEach "com.hbg.module.libkt.custom.sticky  handleTabClick "com.hbg.module.libkt.custom.sticky  isAnimating "com.hbg.module.libkt.custom.sticky  isBottomTabVisible "com.hbg.module.libkt.custom.sticky  kotlin "com.hbg.module.libkt.custom.sticky  let "com.hbg.module.libkt.custom.sticky  requireNotNull "com.hbg.module.libkt.custom.sticky  until "com.hbg.module.libkt.custom.sticky  Behavior /com.hbg.module.libkt.custom.sticky.AppBarLayout  OnOffsetChangedListener /com.hbg.module.libkt.custom.sticky.AppBarLayout  CoIndicator Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin   CoIndicatorStickyBottomTabPlugin Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  Config Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  Context Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  Float Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  Int Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  LinearLayout Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  List Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  Long Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  String Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  
ViewPager2 Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  android Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  androidx Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  apply Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  com Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  config Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  context Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  	emptyList Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  handleCoIndicatorTabClick Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  isBottomTabVisible Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  let Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  performScrollToPosition Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  setTabTitles Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  	tabTitles Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  CoIndicator Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion   CoIndicatorStickyBottomTabPlugin Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  Config Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  LinearLayout Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  android Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  apply Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  	emptyList Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  isBottomTabVisible Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  let Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  OnPageChangeCallback Ncom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.ViewPager2  coordinatorlayout Lcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.androidx  widget ^com.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.androidx.coordinatorlayout  CoordinatorLayout ecom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.androidx.coordinatorlayout.widget  google Gcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.com  android Ncom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.com.google  material Vcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.com.google.android  appbar _com.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.com.google.android.material  AppBarLayout fcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.com.google.android.material.appbar  LayoutParams 4com.hbg.module.libkt.custom.sticky.CoordinatorLayout  Animator 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  AnimatorListenerAdapter 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  AppBarLayout 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Boolean 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Builder 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  CoIndicator 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin   CoIndicatorStickyBottomTabPlugin 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Config 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Context 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  CoordinatorLayout 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	Exception 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Float 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Gravity 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Int 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  IntArray 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  LinearLayout 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  List 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Long 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  StickyBottomTabPlugin 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  String 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	TabLayout 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Unit 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	ViewGroup 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  
ViewPager2 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  android 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  androidx 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  apply 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  checkBottomStickyTabVisibility 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  com 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  config 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  context 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  createGenericTabCopy 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  createStickyTabContainer 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  createStickyTabLayout 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  createTabLayoutCopy 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  destroy 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  disable 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	emptyList 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  enable 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  handleTabClick 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  hideStickyBottomTab 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  isAnimating 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  isBottomTabVisible 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	isEnabled 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  kotlin 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  let 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  offsetChangeListener 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  pageChangeCallback 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  performScrollToPosition 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  removeScrollListener 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  requireNotNull 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  setupScrollListener 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  setupTabSync 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  showStickyBottomTab 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  stickyBottomTabContainer 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  until 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Behavior Ecom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.AppBarLayout  OnOffsetChangedListener Ecom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.AppBarLayout  Config @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  StickyBottomTabPlugin @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  animationDuration @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  appBarLayout @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  apply @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  build @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  context @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  coordinatorLayout @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  onTabClickListener @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  originalTabLayout @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  requireNotNull @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  scrollToPosition @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setAnimationDuration @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setAppBarLayout @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setCoordinatorLayout @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setOnTabClickListener @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setOriginalTabLayout @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setScrollToPosition @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setViewPager @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  tabBackgroundColor @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  tabElevation @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  	viewPager @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  animationDuration ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  appBarLayout ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  coordinatorLayout ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  onTabClickListener ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  originalTabLayout ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  scrollToPosition ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  tabBackgroundColor ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  tabElevation ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  	viewPager ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  LayoutParams Jcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.CoordinatorLayout  OnTabSelectedListener Bcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.TabLayout  Tab Bcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.TabLayout  OnPageChangeCallback Ccom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.ViewPager2  coordinatorlayout Acom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.androidx  widget Scom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.androidx.coordinatorlayout  CoordinatorLayout Zcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.androidx.coordinatorlayout.widget  google <com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.com  android Ccom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.com.google  material Kcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.com.google.android  appbar Tcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.com.google.android.material  AppBarLayout [com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.com.google.android.material.appbar  AppBarLayout ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  CoIndicator ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  CoIndicatorAware ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  Context ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  CoordinatorLayout ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  Float ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  Int ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  List ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  Long ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  StickyBottomTabPlugin ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  String ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  
ViewPager2 ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  apply ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  createForCoIndicator ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  handleCoIndicatorClick ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  setTabTitles Pcom.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory.CoIndicatorAware  OnTabSelectedListener ,com.hbg.module.libkt.custom.sticky.TabLayout  Tab ,com.hbg.module.libkt.custom.sticky.TabLayout  OnPageChangeCallback -com.hbg.module.libkt.custom.sticky.ViewPager2  coordinatorlayout +com.hbg.module.libkt.custom.sticky.androidx  widget =com.hbg.module.libkt.custom.sticky.androidx.coordinatorlayout  CoordinatorLayout Dcom.hbg.module.libkt.custom.sticky.androidx.coordinatorlayout.widget  google &com.hbg.module.libkt.custom.sticky.com  android -com.hbg.module.libkt.custom.sticky.com.google  material 5com.hbg.module.libkt.custom.sticky.com.google.android  appbar >com.hbg.module.libkt.custom.sticky.com.google.android.material  AppBarLayout Ecom.hbg.module.libkt.custom.sticky.com.google.android.material.appbar  StickyBottomTabPlugin com.ttv.demo  StickyBottomTabPluginFactory com.ttv.demo  createForCoIndicator com.ttv.demo  StickyBottomTabPlugin (com.ttv.demo.StickyBottomTabDemoActivity  StickyBottomTabPluginFactory (com.ttv.demo.StickyBottomTabDemoActivity  createForCoIndicator (com.ttv.demo.StickyBottomTabDemoActivity  setupStickyBottomTabPlugin (com.ttv.demo.StickyBottomTabDemoActivity  stickyBottomTabPlugin (com.ttv.demo.StickyBottomTabDemoActivity  requireNotNull kotlin  times 
kotlin.Int  	emptyList kotlin.collections  kotlin 
kotlin.jvm  KClass kotlin.reflect  createBothForCoIndicator android.app.Activity  createBothForCoIndicator android.content.Context  createBothForCoIndicator android.content.ContextWrapper  createBothForCoIndicator  android.view.ContextThemeWrapper  createBothForCoIndicator #androidx.activity.ComponentActivity  createBothForCoIndicator (androidx.appcompat.app.AppCompatActivity  StickyTopTabPlugin #androidx.core.app.ComponentActivity  createBothForCoIndicator #androidx.core.app.ComponentActivity  createBothForCoIndicator &androidx.fragment.app.FragmentActivity  Pair "com.hbg.module.libkt.custom.sticky  StickyTopTabPlugin "com.hbg.module.libkt.custom.sticky  StickyTopTabPluginFactory "com.hbg.module.libkt.custom.sticky  createForCoIndicator "com.hbg.module.libkt.custom.sticky  isTopTabVisible "com.hbg.module.libkt.custom.sticky  Pair ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  StickyTopTabPlugin ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  StickyTopTabPluginFactory ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  createBothForCoIndicator ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  Animator 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  AnimatorListenerAdapter 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  AppBarLayout 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  Boolean 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  Builder 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  Config 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  Context 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  CoordinatorLayout 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  	Exception 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  Float 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  Gravity 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  Int 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  IntArray 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  LinearLayout 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  Long 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  StickyTopTabPlugin 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  	TabLayout 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  Unit 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  	ViewGroup 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  
ViewPager2 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  android 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  apply 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  checkTopStickyTabVisibility 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  config 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  context 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  createGenericTabCopy 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  createStickyTabContainer 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  createStickyTabLayout 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  createTabLayoutCopy 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  destroy 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  disable 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  enable 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  handleTabClick 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  hideStickyTopTab 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  isAnimating 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  	isEnabled 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  isTopTabVisible 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  let 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  offsetChangeListener 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  pageChangeCallback 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  performScrollToShowOriginalTab 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  removeScrollListener 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  requireNotNull 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  setupScrollListener 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  setupTabSync 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  showStickyTopTab 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  stickyTopTabContainer 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  until 5com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin  OnOffsetChangedListener Bcom.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.AppBarLayout  Config =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  StickyTopTabPlugin =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  animationDuration =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  appBarLayout =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  apply =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  build =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  context =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  coordinatorLayout =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  onTabClickListener =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  originalTabLayout =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  requireNotNull =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  setAnimationDuration =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  setAppBarLayout =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  setCoordinatorLayout =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  setOnTabClickListener =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  setOriginalTabLayout =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  setViewPager =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  tabBackgroundColor =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  tabElevation =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  	viewPager =com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Builder  animationDuration <com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Config  appBarLayout <com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Config  coordinatorLayout <com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Config  onTabClickListener <com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Config  originalTabLayout <com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Config  tabBackgroundColor <com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Config  tabElevation <com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Config  	viewPager <com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.Config  OnTabSelectedListener ?com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.TabLayout  Tab ?com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.TabLayout  OnPageChangeCallback @com.hbg.module.libkt.custom.sticky.StickyTopTabPlugin.ViewPager2  StickyTopTabPlugin <com.hbg.module.libkt.custom.sticky.StickyTopTabPluginFactory  createForCoIndicator <com.hbg.module.libkt.custom.sticky.StickyTopTabPluginFactory  handleCoIndicatorClick <com.hbg.module.libkt.custom.sticky.StickyTopTabPluginFactory  StickyTopTabPlugin com.ttv.demo  createBothForCoIndicator com.ttv.demo  StickyTopTabPlugin (com.ttv.demo.StickyBottomTabDemoActivity  createBothForCoIndicator (com.ttv.demo.StickyBottomTabDemoActivity  setupStickyTabPlugins (com.ttv.demo.StickyBottomTabDemoActivity  stickyTopTabPlugin (com.ttv.demo.StickyBottomTabDemoActivity  Pair kotlin  
component1 kotlin.Pair  
component2 kotlin.Pair  width android.view.ViewGroup  
isInitialized 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  showInitialStickyBottomTab 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  #showStickyBottomTabWithoutAnimation 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  contains "com.hbg.module.libkt.custom.sticky  	javaClass "com.hbg.module.libkt.custom.sticky  contains 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  findNestedScrollView 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	javaClass 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  core Acom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.androidx  widget Fcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.androidx.core  NestedScrollView Mcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.androidx.core.widget  core +com.hbg.module.libkt.custom.sticky.androidx  widget 0com.hbg.module.libkt.custom.sticky.androidx.core  NestedScrollView 7com.hbg.module.libkt.custom.sticky.androidx.core.widget  Class 	java.lang  
simpleName java.lang.Class  contains kotlin.collections  	javaClass 
kotlin.jvm  contains 
kotlin.ranges  contains kotlin.sequences  contains kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        