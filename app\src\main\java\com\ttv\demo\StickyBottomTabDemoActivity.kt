package com.ttv.demo

import android.os.Bundle
import android.view.LayoutInflater
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.hbg.module.libkt.custom.indicator.CoIndicator
import com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin
import com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory

/**
 * Tab双向粘性效果演示Activity - 插件化版本
 *
 * 功能说明：
 * 1. 吸顶效果：通过app:layout_isSticky="true"和AppBarLayoutBehavior实现原生吸顶
 * 2. 吸底效果：使用StickyBottomTabPlugin插件实现
 * 3. 插件化设计：展示如何使用可复用的吸底Tab插件
 * 4. 即插即用：通过几行代码就能集成吸底Tab效果
 */
class StickyBottomTabDemoActivity : AppCompatActivity() {

    private lateinit var coIndicator: CoIndicator
    private lateinit var viewPager: ViewPager2
    private lateinit var nestedScrollView: NestedScrollView
    private lateinit var appBarLayout: AppBarLayout
    private lateinit var coordinatorLayout: CoordinatorLayout
    private lateinit var fluentContainer: LinearLayout
    private lateinit var tabLayoutContainer: LinearLayout

    private val tabTitles = listOf("推荐", "关注", "热门", "视频", "直播", "科技", "娱乐", "体育")

    // 吸底Tab插件 - 这就是全部需要的状态管理！
    private var stickyBottomTabPlugin: StickyBottomTabPlugin? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        initViews()
        setupContent()
        setupViewPager()
        setupTabLayout()
        setupStickyBottomTabPlugin()
    }

    private fun initViews() {
        coIndicator = findViewById(R.id.coIndicator)
        viewPager = findViewById(R.id.home_viewPager)
        nestedScrollView = findViewById(R.id.fluent_content_nsv)
        appBarLayout = findViewById(R.id.appBarLayout)
        coordinatorLayout = findViewById(R.id.clLayout)
        fluentContainer = findViewById(R.id.fluent_container)
        tabLayoutContainer = findViewById(R.id.home_feed_linear_tabLayout)
    }

    private fun setupContent() {
        // 添加足够多的内容来演示滚动效果
        for (i in 1..10) {
            val cardView = LayoutInflater.from(this).inflate(R.layout.content_card, null)
            val titleText = cardView.findViewById<TextView>(R.id.tv_card_title)
            val contentText = cardView.findViewById<TextView>(R.id.tv_card_content)

            titleText.text = "演示内容 $i"
            contentText.text = "这是第 $i 个内容卡片。向上滑动可以看到Tab吸顶效果，向下滑动可以看到Tab吸底效果。当Tab即将滚出屏幕时，它会自动在对应位置显示粘性Tab保持可见。"

            fluentContainer.addView(cardView)
        }
    }

    private fun setupViewPager() {
        val adapter = TabPagerAdapter(this, tabTitles)
        viewPager.adapter = adapter
        viewPager.offscreenPageLimit = 1
    }

    private fun setupTabLayout() {
        // 为CoIndicator添加Tab
        tabTitles.forEach { title ->
            coIndicator.addTab(title)
        }

        // 设置ViewPager2与CoIndicator的联动
        TabLayoutMediator(coIndicator, viewPager) { tab, position ->
            tab.text = tabTitles[position]
        }.attach()
    }

    /**
     * 设置吸底Tab插件 - 插件化的核心演示
     *
     * 这里展示了如何用几行代码就集成吸底Tab效果：
     * 1. 使用工厂方法创建插件
     * 2. 传入必要的View引用
     * 3. 配置动画和交互参数
     * 4. 启用插件
     */
    private fun setupStickyBottomTabPlugin() {
        // 使用插件工厂创建吸底Tab插件
        stickyBottomTabPlugin = StickyBottomTabPluginFactory.createForCoIndicator(
            context = this,
            coordinatorLayout = coordinatorLayout,
            appBarLayout = appBarLayout,
            coIndicator = coIndicator,
            viewPager = viewPager,
            tabTitles = tabTitles,
            animationDuration = 300L,        // 动画时长
            scrollToPosition = 0.33f         // 点击后滚动到屏幕1/3位置
        )

        // 启用插件 - 就这么简单！
        stickyBottomTabPlugin?.enable()

        android.util.Log.d("StickyTab", "✅ 吸底Tab插件已启用 - 插件化集成完成！")
    }

    override fun onDestroy() {
        super.onDestroy()
        // 销毁插件，释放资源
        stickyBottomTabPlugin?.destroy()
        android.util.Log.d("StickyTab", "🔄 插件已销毁，资源已释放")
    }

    /**
     * ViewPager适配器
     */
    private class TabPagerAdapter(
        fragmentActivity: FragmentActivity,
        private val tabTitles: List<String>
    ) : FragmentStateAdapter(fragmentActivity) {

        override fun getItemCount(): Int = tabTitles.size

        override fun createFragment(position: Int): Fragment {
            return ContentFragment.newInstance(tabTitles[position], position)
        }
    }
}