package com.hbg.module.libkt.custom.sticky

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.content.Context
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayout

/**
 * 吸底Tab插件 - 可复用的独立组件
 * 
 * 功能特性：
 * 1. 当原始Tab滚出屏幕底部时，在屏幕底部显示吸底Tab
 * 2. 支持流畅的滑入/滑出动画效果
 * 3. 点击吸底Tab后自动滚动到指定位置
 * 4. 完整的状态同步机制
 * 5. 即插即用的集成方式
 * 
 * 使用示例：
 * ```kotlin
 * val plugin = StickyBottomTabPlugin.Builder(context)
 *     .setCoordinatorLayout(coordinatorLayout)
 *     .setAppBarLayout(appBarLayout)
 *     .setOriginalTabLayout(tabLayout)
 *     .setViewPager(viewPager)
 *     .setAnimationDuration(300)
 *     .setScrollToPosition(0.33f)
 *     .build()
 * 
 * plugin.enable()
 * ```
 */
open class StickyBottomTabPlugin protected constructor(
    protected val context: Context,
    protected val config: Config
) {
    
    // 插件状态
    private var isEnabled = false
    private var isBottomTabVisible = false
    private var isAnimating = false
    private var stickyBottomTabContainer: LinearLayout? = null
    
    // 滚动监听器
    private var offsetChangeListener: AppBarLayout.OnOffsetChangedListener? = null
    private var pageChangeCallback: ViewPager2.OnPageChangeCallback? = null
    
    /**
     * 配置类 - 封装所有可配置参数
     */
    data class Config(
        val coordinatorLayout: CoordinatorLayout,
        val appBarLayout: AppBarLayout,
        val originalTabLayout: ViewGroup,
        val viewPager: ViewPager2?,
        val animationDuration: Long = 300L,
        val scrollToPosition: Float = 0.33f, // 滚动到屏幕的1/3位置
        val tabBackgroundColor: Int = 0xFFFFFFFF.toInt(),
        val tabElevation: Float = 8f,
        val onTabClickListener: ((position: Int) -> Unit)? = null
    )
    
    /**
     * 建造者模式 - 提供简洁的API接口
     */
    class Builder(private val context: Context) {
        private var coordinatorLayout: CoordinatorLayout? = null
        private var appBarLayout: AppBarLayout? = null
        private var originalTabLayout: ViewGroup? = null
        private var viewPager: ViewPager2? = null
        private var animationDuration: Long = 300L
        private var scrollToPosition: Float = 0.33f
        private var tabBackgroundColor: Int = 0xFFFFFFFF.toInt()
        private var tabElevation: Float = 8f
        private var onTabClickListener: ((position: Int) -> Unit)? = null
        
        fun setCoordinatorLayout(coordinatorLayout: CoordinatorLayout) = apply {
            this.coordinatorLayout = coordinatorLayout
        }
        
        fun setAppBarLayout(appBarLayout: AppBarLayout) = apply {
            this.appBarLayout = appBarLayout
        }
        
        fun setOriginalTabLayout(tabLayout: ViewGroup) = apply {
            this.originalTabLayout = tabLayout
        }
        
        fun setViewPager(viewPager: ViewPager2?) = apply {
            this.viewPager = viewPager
        }
        
        fun setAnimationDuration(duration: Long) = apply {
            this.animationDuration = duration
        }
        
        fun setScrollToPosition(position: Float) = apply {
            this.scrollToPosition = position
        }
        
        fun setTabBackgroundColor(color: Int) = apply {
            this.tabBackgroundColor = color
        }
        
        fun setTabElevation(elevation: Float) = apply {
            this.tabElevation = elevation
        }
        
        fun setOnTabClickListener(listener: (position: Int) -> Unit) = apply {
            this.onTabClickListener = listener
        }
        
        fun build(): StickyBottomTabPlugin {
            requireNotNull(coordinatorLayout) { "CoordinatorLayout is required" }
            requireNotNull(appBarLayout) { "AppBarLayout is required" }
            requireNotNull(originalTabLayout) { "Original TabLayout is required" }
            
            val config = Config(
                coordinatorLayout = coordinatorLayout!!,
                appBarLayout = appBarLayout!!,
                originalTabLayout = originalTabLayout!!,
                viewPager = viewPager,
                animationDuration = animationDuration,
                scrollToPosition = scrollToPosition,
                tabBackgroundColor = tabBackgroundColor,
                tabElevation = tabElevation,
                onTabClickListener = onTabClickListener
            )
            
            return StickyBottomTabPlugin(context, config)
        }
    }
    
    /**
     * 启用插件
     */
    fun enable() {
        if (isEnabled) return
        
        isEnabled = true
        setupScrollListener()
        
        android.util.Log.d("StickyBottomTabPlugin", "插件已启用")
    }
    
    /**
     * 禁用插件
     */
    fun disable() {
        if (!isEnabled) return
        
        isEnabled = false
        removeScrollListener()
        hideStickyBottomTab()
        
        android.util.Log.d("StickyBottomTabPlugin", "插件已禁用")
    }
    
    /**
     * 检查插件是否已启用
     */
    fun isEnabled(): Boolean = isEnabled
    
    /**
     * 检查吸底Tab是否正在显示
     */
    fun isBottomTabVisible(): Boolean = isBottomTabVisible
    
    /**
     * 设置滚动监听器
     */
    private fun setupScrollListener() {
        offsetChangeListener = AppBarLayout.OnOffsetChangedListener { _, verticalOffset ->
            if (isEnabled && !isAnimating) {
                checkBottomStickyTabVisibility(verticalOffset)
            }
        }
        config.appBarLayout.addOnOffsetChangedListener(offsetChangeListener)
    }
    
    /**
     * 移除滚动监听器
     */
    private fun removeScrollListener() {
        offsetChangeListener?.let { listener ->
            config.appBarLayout.removeOnOffsetChangedListener(listener)
        }
        offsetChangeListener = null

        pageChangeCallback?.let { callback ->
            config.viewPager?.unregisterOnPageChangeCallback(callback)
        }
        pageChangeCallback = null
    }

    /**
     * 检测并更新吸底Tab的显示状态
     */
    private fun checkBottomStickyTabVisibility(verticalOffset: Int) {
        // 获取原始Tab的屏幕位置信息
        val location = IntArray(2)
        config.originalTabLayout.getLocationOnScreen(location)
        val tabScreenY = location[1]
        val tabHeight = config.originalTabLayout.height
        val screenHeight = context.resources.displayMetrics.heightPixels
        val tabBottomY = tabScreenY + tabHeight

        // 判断Tab状态
        val isTabScrolledOutOfBottom = tabScreenY >= screenHeight
        val isTabVisible = tabScreenY < screenHeight && tabBottomY > 0

        android.util.Log.d("StickyBottomTabPlugin",
            "位置检测: verticalOffset=$verticalOffset, " +
            "tabScreenY=$tabScreenY, tabBottomY=$tabBottomY, " +
            "screenHeight=$screenHeight, " +
            "isTabVisible=$isTabVisible, " +
            "isTabScrolledOutOfBottom=$isTabScrolledOutOfBottom")

        when {
            isTabScrolledOutOfBottom && !isBottomTabVisible -> {
                // Tab滚出屏幕底部，显示吸底Tab
                android.util.Log.d("StickyBottomTabPlugin", "显示吸底Tab")
                showStickyBottomTab()
            }
            isTabVisible && isBottomTabVisible -> {
                // Tab重新可见，隐藏吸底Tab
                android.util.Log.d("StickyBottomTabPlugin", "隐藏吸底Tab")
                hideStickyBottomTab()
            }
        }
    }

    /**
     * 显示吸底Tab
     */
    private fun showStickyBottomTab() {
        if (isAnimating || isBottomTabVisible || stickyBottomTabContainer != null) return

        android.util.Log.d("StickyBottomTabPlugin", "开始创建吸底Tab")
        isAnimating = true
        isBottomTabVisible = true

        // 创建吸底Tab容器
        stickyBottomTabContainer = createStickyTabContainer()
        val stickyTabLayout = createStickyTabLayout()
        stickyBottomTabContainer?.addView(stickyTabLayout)

        // 添加到CoordinatorLayout底部
        val layoutParams = CoordinatorLayout.LayoutParams(
            CoordinatorLayout.LayoutParams.MATCH_PARENT,
            CoordinatorLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.BOTTOM
        }

        config.coordinatorLayout.addView(stickyBottomTabContainer, layoutParams)

        // 入场动画：从底部滑入
        stickyBottomTabContainer?.let { container ->
            container.translationY = container.height.toFloat()
            container.animate()
                .translationY(0f)
                .setDuration(config.animationDuration)
                .setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        isAnimating = false
                        android.util.Log.d("StickyBottomTabPlugin", "吸底Tab入场动画完成")
                    }
                    override fun onAnimationCancel(animation: Animator) {
                        isAnimating = false
                    }
                })
                .start()
        }

        // 设置状态同步
        setupTabSync(stickyTabLayout)
    }

    /**
     * 隐藏吸底Tab
     */
    private fun hideStickyBottomTab() {
        if (isAnimating || !isBottomTabVisible || stickyBottomTabContainer == null) return

        android.util.Log.d("StickyBottomTabPlugin", "开始隐藏吸底Tab")
        isAnimating = true
        isBottomTabVisible = false

        val containerToRemove = stickyBottomTabContainer
        stickyBottomTabContainer = null

        // 退场动画：向底部滑出
        containerToRemove?.animate()
            ?.translationY(containerToRemove.height.toFloat())
            ?.setDuration(config.animationDuration)
            ?.setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    try {
                        config.coordinatorLayout.removeView(containerToRemove)
                        android.util.Log.d("StickyBottomTabPlugin", "吸底Tab退场动画完成")
                    } catch (e: Exception) {
                        android.util.Log.e("StickyBottomTabPlugin", "移除吸底Tab时发生异常", e)
                    }
                    isAnimating = false
                }
                override fun onAnimationCancel(animation: Animator) {
                    isAnimating = false
                }
            })
            ?.start()
    }

    /**
     * 创建吸底Tab容器
     */
    private fun createStickyTabContainer(): LinearLayout {
        return LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER_VERTICAL
            setBackgroundColor(config.tabBackgroundColor)
            elevation = config.tabElevation
        }
    }

    /**
     * 创建吸底Tab布局
     * 支持不同类型的TabLayout
     */
    private fun createStickyTabLayout(): ViewGroup {
        return when (val originalTab = config.originalTabLayout) {
            is TabLayout -> {
                createTabLayoutCopy(originalTab)
            }
            else -> {
                // 对于自定义Tab组件，尝试通过反射或接口复制
                createGenericTabCopy(originalTab)
            }
        }
    }

    /**
     * 创建TabLayout的副本
     */
    private fun createTabLayoutCopy(originalTabLayout: TabLayout): TabLayout {
        val stickyTabLayout = TabLayout(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                1f
            )
            // 复制原始TabLayout的样式属性
            tabMode = originalTabLayout.tabMode
            tabGravity = originalTabLayout.tabGravity
        }

        // 复制所有Tab
        for (i in 0 until originalTabLayout.tabCount) {
            val originalTab = originalTabLayout.getTabAt(i)
            val newTab = stickyTabLayout.newTab()
            newTab.text = originalTab?.text
            newTab.icon = originalTab?.icon
            stickyTabLayout.addTab(newTab)
        }

        // 同步选中状态
        val selectedPosition = originalTabLayout.selectedTabPosition
        if (selectedPosition >= 0 && selectedPosition < stickyTabLayout.tabCount) {
            stickyTabLayout.getTabAt(selectedPosition)?.select()
        }

        // 设置点击监听
        stickyTabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let {
                    handleTabClick(it.position)
                }
            }
            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {
                tab?.let {
                    handleTabClick(it.position)
                }
            }
        })

        return stickyTabLayout
    }

    /**
     * 创建通用Tab副本（用于自定义Tab组件）
     */
    private fun createGenericTabCopy(originalTab: ViewGroup): ViewGroup {
        // 这里可以根据具体的自定义Tab组件类型进行处理
        // 暂时返回一个简单的LinearLayout作为占位符
        return LinearLayout(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                1f
            )
            // 可以在这里添加更多的通用处理逻辑
        }
    }

    /**
     * 处理Tab点击事件
     */
    private fun handleTabClick(position: Int) {
        android.util.Log.d("StickyBottomTabPlugin", "吸底Tab点击: position=$position")

        // 先执行自定义的点击监听器
        config.onTabClickListener?.invoke(position)

        // 切换ViewPager页面
        config.viewPager?.setCurrentItem(position, true)

        // 执行滚动到指定位置的动画
        performScrollToPosition()
    }

    /**
     * 滚动到指定位置（默认屏幕1/3处）
     */
    protected open fun performScrollToPosition() {
        config.coordinatorLayout.post {
            val screenHeight = context.resources.displayMetrics.heightPixels
            val targetY = (screenHeight * config.scrollToPosition).toInt()

            // 先完全展开AppBarLayout
            config.appBarLayout.setExpanded(true, false)

            // 等待布局完成后计算需要的滚动距离
            config.coordinatorLayout.post {
                val tabLocation = IntArray(2)
                config.originalTabLayout.getLocationOnScreen(tabLocation)
                val currentTabY = tabLocation[1]

                // 计算需要向下滚动的距离
                val scrollDistance = currentTabY - targetY

                if (scrollDistance > 0) {
                    // 使用AppBarLayout的Behavior来精确控制滚动
                    val behavior = (config.appBarLayout.layoutParams as? CoordinatorLayout.LayoutParams)?.behavior
                    if (behavior is AppBarLayout.Behavior) {
                        val totalScrollRange = config.appBarLayout.totalScrollRange
                        val targetOffset = kotlin.math.min(totalScrollRange, scrollDistance)

                        // 使用动画平滑滚动到目标位置
                        val animator = android.animation.ValueAnimator.ofInt(0, targetOffset)
                        animator.duration = 400
                        animator.interpolator = android.view.animation.DecelerateInterpolator()
                        animator.addUpdateListener { animation ->
                            val offset = animation.animatedValue as Int
                            behavior.topAndBottomOffset = -offset
                            config.appBarLayout.requestLayout()
                        }
                        animator.start()
                    }
                }

                android.util.Log.d("StickyBottomTabPlugin", "滚动到指定位置完成")
            }
        }
    }

    /**
     * 设置Tab状态同步
     */
    private fun setupTabSync(stickyTabLayout: ViewGroup) {
        config.viewPager?.let { viewPager ->
            pageChangeCallback = object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    // 同步吸底Tab的选中状态
                    if (isBottomTabVisible && stickyTabLayout is TabLayout) {
                        stickyTabLayout.getTabAt(position)?.select()
                    }
                }
            }
            viewPager.registerOnPageChangeCallback(pageChangeCallback!!)
        }
    }

    /**
     * 销毁插件，释放资源
     */
    fun destroy() {
        disable()
        android.util.Log.d("StickyBottomTabPlugin", "插件已销毁")
    }
}
